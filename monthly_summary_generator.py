#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
月度汇总数据生成器
读取钉钉表格Sheet1的数据，生成Sheet2的按月汇总报告
"""

import json
import re
from datetime import datetime
from collections import defaultdict
from typing import Dict, List, Any
from dingtalk_sheet_utils import DingTalkSheetUtils


class MonthlySummaryGenerator:
    def __init__(self, config_file="config.json"):
        """
        初始化月度汇总生成器
        
        Args:
            config_file: 配置文件路径
        """
        self.config = self.load_config(config_file)
        self.dingtalk_sheet = None
        self.init_dingtalk_sheet()
    
    def load_config(self, config_file: str) -> dict:
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"✅ 配置文件加载成功: {config_file}")
            return config
        except Exception as e:
            print(f"❌ 配置文件加载失败: {str(e)}")
            return {}
    
    def init_dingtalk_sheet(self):
        """初始化钉钉表格工具"""
        try:
            sheet_config = self.config.get('dingtalk', {}).get('sheet', {})
            if not sheet_config.get('enabled', False):
                print("⚠️ 钉钉表格功能未启用")
                return
            
            self.dingtalk_sheet = DingTalkSheetUtils(self.config)
            print("✅ 钉钉表格工具初始化完成")
        except Exception as e:
            print(f"❌ 钉钉表格工具初始化失败: {str(e)}")
    
    def read_sheet1_data(self, sheet_id: str) -> List[Dict]:
        """
        读取仓库绩效明细的所有数据

        Args:
            sheet_id: 工作表ID

        Returns:
            解析后的数据列表
        """
        if not self.dingtalk_sheet:
            print("❌ 钉钉表格工具未初始化")
            return []

        print("🔍 开始读取仓库绩效明细数据...")
        
        # 动态读取数据，自动检测实际数据范围
        try:
            # 使用动态范围检测，确保读取到所有数据
            raw_data = self.get_dynamic_sheet_data(sheet_id)
            if not raw_data:
                print("⚠️ 未读取到任何数据")
                return []
            
            print(f"📊 读取到 {len(raw_data)} 行原始数据")
            
            # 解析数据
            parsed_data = self.parse_sheet1_data(raw_data)
            print(f"✅ 成功解析 {len(parsed_data)} 条有效数据记录")
            
            return parsed_data
            
        except Exception as e:
            print(f"❌ 读取仓库绩效明细数据失败: {str(e)}")
            return []

    def get_dynamic_sheet_data(self, sheet_id: str) -> List[List]:
        """
        动态获取工作表数据，自动检测实际数据范围

        Args:
            sheet_id: 工作表ID

        Returns:
            工作表的所有数据
        """
        print("🔍 开始动态检测数据范围...")

        # 策略1: 先尝试读取一个较大的范围来检测实际数据边界
        initial_range = "A1:C5000"  # 初始检测范围
        print(f"📊 尝试读取初始范围: {initial_range}")

        try:
            raw_data = self.dingtalk_sheet.get_cell_range(sheet_id, initial_range)
            if not raw_data:
                print("⚠️ 初始范围未读取到数据，尝试更小范围")
                # 如果大范围失败，尝试较小范围
                fallback_range = "A1:C1000"
                print(f"📊 尝试备用范围: {fallback_range}")
                raw_data = self.dingtalk_sheet.get_cell_range(sheet_id, fallback_range)

            if raw_data:
                # 找到实际的数据边界
                last_data_row = self.find_last_data_row(raw_data)
                print(f"📍 检测到最后数据行: {last_data_row}")

                # 如果检测到的数据行数较少，可能是范围不够，返回所有读取的数据
                if last_data_row > 0:
                    # 只返回有效数据范围，减少处理时间
                    effective_data = raw_data[:last_data_row + 10]  # 多保留10行作为缓冲
                    print(f"✅ 返回有效数据范围: {len(effective_data)} 行")
                    return effective_data
                else:
                    print("✅ 返回所有读取数据")
                    return raw_data
            else:
                print("❌ 无法读取任何数据")
                return []

        except Exception as e:
            print(f"❌ 动态数据检测失败: {str(e)}")
            # 降级到固定范围
            print("🔄 降级到固定范围读取...")
            return self.dingtalk_sheet.get_cell_range(sheet_id, "A1:C2000")

    def find_last_data_row(self, raw_data: List[List]) -> int:
        """
        找到最后一行有效数据的行号

        Args:
            raw_data: 原始数据

        Returns:
            最后数据行的索引（从0开始）
        """
        last_row = -1

        for i in range(len(raw_data) - 1, -1, -1):  # 从后往前遍历
            row = raw_data[i]
            if row and any(str(cell).strip() for cell in row if cell):  # 检查是否有非空内容
                last_row = i
                break

        return last_row

    def parse_sheet1_data(self, raw_data: List[List]) -> List[Dict]:
        """
        解析仓库绩效明细的原始数据
        
        Args:
            raw_data: 原始数据
            
        Returns:
            解析后的数据记录列表
        """
        parsed_records = []
        current_record = {}
        
        for i, row in enumerate(raw_data):
            if not row or len(row) < 3:
                continue
            
            # 检查是否是时间戳行
            if self.is_timestamp_row(row):
                # 如果已有记录，保存它
                if current_record:
                    parsed_records.append(current_record)
                
                # 开始新记录
                current_record = {
                    'timestamp': self.parse_timestamp(row),
                    'row_index': i
                }
                continue
            
            # 解析数据行
            if current_record and len(row) >= 3:
                field_name = str(row[0]).strip() if row[0] else ""
                value_b = row[1] if len(row) > 1 else ""
                value_c = row[2] if len(row) > 2 else ""
                
                # 根据字段名称解析数据
                if field_name == "待补货":
                    # 待补货行：B列是品数，C列是订单数
                    current_record['待补货品数'] = self.safe_int(value_b)
                    current_record['待补货订单数'] = self.safe_int(value_c)
                elif field_name == "待处理":
                    # 待处理行：B列是品数，C列是订单数
                    current_record['待处理品数'] = self.safe_int(value_b)
                    current_record['待处理订单数'] = self.safe_int(value_c)
                elif field_name == "发货数据":
                    # 发货数据标题行，跳过
                    pass
                elif field_name == "现货订单数":
                    # 现货订单数行：B列是订单数
                    current_record['现货订单数'] = self.safe_int(value_b)
                elif field_name == "现货订单发货总重量":
                    # 现货订单发货总重量行：B列是重量（去掉kg单位）
                    weight_text = str(value_b) if value_b else ""
                    if weight_text.endswith('kg'):
                        weight_text = weight_text[:-2]
                    current_record['现货订单发货总重量'] = self.safe_float(weight_text)
                elif field_name == "定制订单数":
                    # 定制订单数行：B列是订单数
                    current_record['定制订单数'] = self.safe_int(value_b)
                elif field_name == "定制订单发货总重量":
                    # 定制订单发货总重量行：B列是重量（去掉kg单位）
                    weight_text = str(value_b) if value_b else ""
                    if weight_text.endswith('kg'):
                        weight_text = weight_text[:-2]
                    current_record['定制订单发货总重量'] = self.safe_float(weight_text)
                elif field_name == "已审核-定制":
                    # 已审核-定制行：B列是订单数
                    current_record['已审核-定制'] = self.safe_int(value_b)
                elif field_name == "已审核-现货":
                    # 已审核-现货行：B列是订单数
                    current_record['已审核-现货'] = self.safe_int(value_b)
                elif field_name == "生产单完成进度":
                    # 生产单完成进度行：B列是进度文本
                    progress_text = str(value_b) if value_b else ""
                    current_record['生产单完成进度'] = progress_text
                elif field_name == "总计奖惩":
                    # 总计奖惩行：B列是奖惩金额
                    reward_text = str(value_b) if value_b else ""
                    current_record['奖惩金额'] = self.parse_reward_amount(reward_text)

        
        # 保存最后一条记录
        if current_record:
            parsed_records.append(current_record)
        
        # 过滤有效记录
        valid_records = [r for r in parsed_records if r.get('timestamp')]
        
        return valid_records
    
    def is_timestamp_row(self, row: List) -> bool:
        """判断是否是时间戳行"""
        if not row or len(row) < 2:
            return False
        
        # 检查第一列是否包含时间相关关键词
        first_cell = str(row[0]).strip() if row[0] else ""
        if "数据生成时间" in first_cell or "数据统计时间" in first_cell or "时间" in first_cell:
            return True
        
        # 检查第二列是否是时间格式
        second_cell = str(row[1]).strip() if len(row) > 1 and row[1] else ""
        if self.is_datetime_string(second_cell):
            return True
        
        return False
    
    def is_datetime_string(self, text: str) -> bool:
        """判断字符串是否是日期时间格式"""
        if not text:
            return False
        
        # 常见的日期时间格式
        datetime_patterns = [
            r'\d{4}[/-]\d{1,2}[/-]\d{1,2}',  # 2025/01/02 或 2025-01-02
            r'\d{4}[/-]\d{1,2}[/-]\d{1,2}\s+\d{1,2}:\d{1,2}',  # 2025/01/02 12:34
            r'\d{4}[/-]\d{1,2}[/-]\d{1,2}\s+\d{1,2}:\d{1,2}:\d{1,2}',  # 2025/01/02 12:34:56
        ]
        
        for pattern in datetime_patterns:
            if re.search(pattern, text):
                return True
        
        return False
    
    def parse_timestamp(self, row: List) -> datetime:
        """解析时间戳"""
        try:
            # 尝试从第二列解析时间
            if len(row) > 1 and row[1]:
                time_str = str(row[1]).strip()
                
                # 尝试多种时间格式
                time_formats = [
                    '%Y/%m/%d %H:%M:%S',
                    '%Y-%m-%d %H:%M:%S',
                    '%Y/%m/%d %H:%M',
                    '%Y-%m-%d %H:%M',
                    '%Y/%m/%d',
                    '%Y-%m-%d',
                ]
                
                for fmt in time_formats:
                    try:
                        return datetime.strptime(time_str, fmt)
                    except ValueError:
                        continue
            
            # 如果解析失败，返回当前时间
            return datetime.now()
            
        except Exception as e:
            print(f"⚠️ 时间戳解析失败: {str(e)}")
            return datetime.now()
    
    def safe_int(self, value) -> int:
        """安全转换为整数"""
        if value is None or value == "":
            return 0
        try:
            # 移除逗号和其他非数字字符
            clean_value = str(value).replace(',', '').replace(' ', '')
            return int(float(clean_value))
        except (ValueError, TypeError):
            return 0
    
    def safe_float(self, value) -> float:
        """安全转换为浮点数"""
        if value is None or value == "":
            return 0.0
        try:
            # 移除逗号和其他非数字字符（保留小数点）
            clean_value = str(value).replace(',', '').replace(' ', '')
            return float(clean_value)
        except (ValueError, TypeError):
            return 0.0
    
    def parse_reward_amount(self, text: str) -> str:
        """解析奖惩金额"""
        if not text:
            return "0元"
        
        # 提取数字和符号
        text = str(text).strip()
        
        # 查找金额模式
        amount_pattern = r'([+-]?\d+(?:\.\d+)?)\s*元'
        match = re.search(amount_pattern, text)
        
        if match:
            amount = match.group(1)
            return f"{amount}元"
        
        # 如果没有找到标准格式，尝试提取数字
        number_pattern = r'([+-]?\d+(?:\.\d+)?)'
        match = re.search(number_pattern, text)
        
        if match:
            amount = match.group(1)
            return f"{amount}元"
        
        return "0元"

    def generate_monthly_summaries(self, data_records: List[Dict]) -> Dict[str, Dict]:
        """
        生成月度汇总数据

        Args:
            data_records: 解析后的数据记录

        Returns:
            按月份组织的汇总数据
        """
        monthly_data = defaultdict(list)

        # 按月份分组
        for record in data_records:
            if record.get('timestamp'):
                month_key = record['timestamp'].strftime('%Y-%m')
                monthly_data[month_key].append(record)

        # 计算每月汇总
        monthly_summaries = {}
        for month, records in monthly_data.items():
            monthly_summaries[month] = self.calculate_month_summary(month, records)

        return monthly_summaries

    def calculate_month_summary(self, month: str, records: List[Dict]) -> Dict:
        """
        计算单月汇总数据

        Args:
            month: 月份字符串 (YYYY-MM)
            records: 该月的所有记录

        Returns:
            月度汇总数据
        """
        if not records:
            return {}

        # 提取数值数据
        待补货品数_list = [r.get('待补货品数', 0) for r in records if r.get('待补货品数', 0) > 0]
        待补货订单数_list = [r.get('待补货订单数', 0) for r in records if r.get('待补货订单数', 0) > 0]
        待处理品数_list = [r.get('待处理品数', 0) for r in records if r.get('待处理品数', 0) > 0]
        待处理订单数_list = [r.get('待处理订单数', 0) for r in records if r.get('待处理订单数', 0) > 0]
        现货订单数_list = [r.get('现货订单数', 0) for r in records if r.get('现货订单数', 0) > 0]
        定制订单数_list = [r.get('定制订单数', 0) for r in records if r.get('定制订单数', 0) > 0]
        发货总重量_list = [r.get('发货总重量', 0) for r in records if r.get('发货总重量', 0) > 0]
        已审核定制_list = [r.get('已审核-定制', 0) for r in records if r.get('已审核-定制', 0) > 0]
        已审核现货_list = [r.get('已审核-现货', 0) for r in records if r.get('已审核-现货', 0) > 0]

        # 解析奖惩金额
        奖惩金额_list = []
        for r in records:
            reward_text = r.get('奖惩金额', '0元')
            amount = self.extract_reward_number(reward_text)
            if amount is not None:
                奖惩金额_list.append(amount)

        # 计算统计数据
        summary = {
            'month': month,
            'month_display': self.format_month_display(month),
            'record_count': len(records),
            'daily_data': [],
            'statistics': {}
        }

        # 每日数据
        for record in sorted(records, key=lambda x: x.get('timestamp', datetime.now())):
            # 计算发货总重量（现货+定制）
            stock_weight = record.get('现货订单发货总重量', 0)
            custom_weight = record.get('定制订单发货总重量', 0)
            total_weight = stock_weight + custom_weight

            daily_data = {
                'date': record.get('timestamp', datetime.now()).strftime('%m-%d'),
                'date_full': record.get('timestamp', datetime.now()).strftime('%Y-%m-%d'),
                '待补货品数': record.get('待补货品数', 0),
                '待补货订单数': record.get('待补货订单数', 0),
                '待处理品数': record.get('待处理品数', 0),
                '待处理订单数': record.get('待处理订单数', 0),
                '现货订单数': record.get('现货订单数', 0),
                '定制订单数': record.get('定制订单数', 0),
                '发货总重量': total_weight,  # 现货+定制重量的总和
                '已审核-定制': record.get('已审核-定制', 0),
                '已审核-现货': record.get('已审核-现货', 0),
                '生产单完成进度': record.get('生产单完成进度', ''),
                '奖惩金额': record.get('奖惩金额', '0元')
            }
            summary['daily_data'].append(daily_data)

        # 统计数据
        if 待补货品数_list:
            summary['statistics']['待补货品数'] = {
                '平均值': round(sum(待补货品数_list) / len(待补货品数_list), 1),
                '最大值': max(待补货品数_list),
                '最小值': min(待补货品数_list)
            }

        if 待补货订单数_list:
            summary['statistics']['待补货订单数'] = {
                '平均值': round(sum(待补货订单数_list) / len(待补货订单数_list), 1),
                '最大值': max(待补货订单数_list),
                '最小值': min(待补货订单数_list)
            }

        if 待处理品数_list:
            summary['statistics']['待处理品数'] = {
                '平均值': round(sum(待处理品数_list) / len(待处理品数_list), 1),
                '最大值': max(待处理品数_list),
                '最小值': min(待处理品数_list)
            }

        if 待处理订单数_list:
            summary['statistics']['待处理订单数'] = {
                '平均值': round(sum(待处理订单数_list) / len(待处理订单数_list), 1),
                '最大值': max(待处理订单数_list),
                '最小值': min(待处理订单数_list)
            }

        if 现货订单数_list:
            summary['statistics']['现货订单数'] = {
                '平均值': round(sum(现货订单数_list) / len(现货订单数_list), 1),
                '最大值': max(现货订单数_list),
                '最小值': min(现货订单数_list),
                '总计': sum(现货订单数_list)
            }

        if 定制订单数_list:
            summary['statistics']['定制订单数'] = {
                '平均值': round(sum(定制订单数_list) / len(定制订单数_list), 1),
                '最大值': max(定制订单数_list),
                '最小值': min(定制订单数_list),
                '总计': sum(定制订单数_list)
            }

        if 发货总重量_list:
            summary['statistics']['发货总重量'] = {
                '平均值': round(sum(发货总重量_list) / len(发货总重量_list), 2),
                '最大值': max(发货总重量_list),
                '最小值': min(发货总重量_list),
                '总计': round(sum(发货总重量_list), 2)
            }

        if 已审核定制_list:
            summary['statistics']['已审核-定制'] = {
                '平均值': round(sum(已审核定制_list) / len(已审核定制_list), 1),
                '最大值': max(已审核定制_list),
                '最小值': min(已审核定制_list),
                '总计': sum(已审核定制_list)
            }

        if 已审核现货_list:
            summary['statistics']['已审核-现货'] = {
                '平均值': round(sum(已审核现货_list) / len(已审核现货_list), 1),
                '最大值': max(已审核现货_list),
                '最小值': min(已审核现货_list),
                '总计': sum(已审核现货_list)
            }

        if 奖惩金额_list:
            summary['statistics']['奖惩金额'] = {
                '平均值': f"{round(sum(奖惩金额_list) / len(奖惩金额_list), 1)}元",
                '最大值': f"{max(奖惩金额_list)}元",
                '最小值': f"{min(奖惩金额_list)}元",
                '总计': f"{sum(奖惩金额_list)}元"
            }

        return summary

    def extract_reward_number(self, reward_text: str) -> float:
        """从奖惩文本中提取数值"""
        if not reward_text:
            return 0.0

        # 移除"元"字符
        text = str(reward_text).replace('元', '').strip()

        try:
            return float(text)
        except (ValueError, TypeError):
            return 0.0

    def format_month_display(self, month: str) -> str:
        """格式化月份显示"""
        try:
            year, month_num = month.split('-')
            return f"{year}年{month_num.zfill(2)}月数据汇总"
        except:
            return f"{month}数据汇总"

    def write_monthly_summaries_to_sheet2(self, monthly_summaries: Dict[str, Dict]) -> bool:
        """
        将月度汇总数据写入仓库绩效汇总

        Args:
            monthly_summaries: 月度汇总数据

        Returns:
            是否写入成功
        """
        if not self.dingtalk_sheet:
            print("❌ 钉钉表格工具未初始化")
            return False

        try:
            # 获取工作表列表
            sheets = self.dingtalk_sheet.get_sheets_list()
            if len(sheets) < 2:
                print("❌ 表格中没有仓库绩效汇总工作表，请先创建")
                return False

            # 从配置文件中获取第二个工作表名称
            sheet2_name = self.config.get('dingtalk', {}).get('sheet', {}).get('sheet2_name', '仓库绩效汇总')

            # 查找匹配的工作表
            sheet2_id = None
            for sheet in sheets:
                if sheet.get('name') == sheet2_name:
                    sheet2_id = sheet.get('id')
                    break

            # 如果没找到匹配的名称，使用第二个工作表
            if not sheet2_id:
                sheet2_id = sheets[1].get('id', sheet2_name)
                print(f"⚠️ 未找到名为'{sheet2_name}'的工作表，使用第二个工作表")

            print(f"📋 开始写入{sheet2_name}: {sheet2_id}")

            # 准备写入数据
            print("📝 准备写入数据...")

            all_data = []  # 存储所有要写入的数据

            # 按月份排序准备数据
            for month in sorted(monthly_summaries.keys()):
                summary = monthly_summaries[month]
                print(f"📝 准备 {summary['month_display']} 数据...")

                # 月度标题行
                all_data.append([summary['month_display'], '', '', '', '', '', '', '', '', '', '', ''])

                # 表头行
                all_data.append(['日期', '待补货品数', '待补货订单', '待处理品数', '待处理订单', '现货订单', '定制订单', '发货重量', '已审核定制', '已审核现货', '生产进度', '奖惩金额'])

                # 每日数据行
                for daily in summary.get('daily_data', []):
                    row_data = [
                        str(daily['date']),
                        str(daily['待补货品数']),
                        str(daily['待补货订单数']),
                        str(daily['待处理品数']),
                        str(daily['待处理订单数']),
                        str(daily['现货订单数']),
                        str(daily['定制订单数']),
                        str(daily['发货总重量']),
                        str(daily['已审核-定制']),
                        str(daily['已审核-现货']),
                        str(daily['生产单完成进度']),
                        str(daily['奖惩金额'])
                    ]
                    all_data.append(row_data)

                # 不添加统计数据，按用户要求简化

                # 添加空行分隔
                all_data.append(['', '', '', '', '', '', '', '', '', '', '', ''])

            # 使用现有的批量写入方法
            print(f"📊 开始写入 {len(all_data)} 行数据到{sheet2_name}...")

            # 计算写入范围
            end_col = chr(ord('A') + 11)  # L列 (12列数据)
            write_range = f"A1:{end_col}{len(all_data)}"

            success = self.dingtalk_sheet.write_cell_range(sheet2_id, write_range, all_data)

            if success:
                print(f"✅ 数据写入成功")

                # 设置格式：居中对齐和合并单元格
                self.apply_sheet2_formatting(sheet2_id, len(all_data), monthly_summaries)

                return True
            else:
                print(f"❌ 数据写入失败")
                return False

        except Exception as e:
            sheet2_name = self.config.get('dingtalk', {}).get('sheet', {}).get('sheet2_name', '仓库绩效汇总')
            print(f"❌ 写入{sheet2_name}失败: {str(e)}")
            return False

    def apply_sheet2_formatting(self, sheet_id: str, total_rows: int, monthly_summaries: Dict):
        """应用仓库绩效汇总的格式设置：居中对齐和合并单元格"""
        try:
            print("🎨 应用格式设置...")

            # 1. 设置所有数据居中对齐
            self.set_center_alignment(sheet_id, total_rows)

            # 2. 合并标题行
            self.merge_title_cells(sheet_id, monthly_summaries)

        except Exception as e:
            print(f"⚠️ 格式设置异常: {str(e)}")

    def set_center_alignment(self, sheet_id: str, total_rows: int):
        """设置所有数据单元格居中对齐"""
        if not self.dingtalk_sheet or total_rows == 0:
            return

        print("📐 设置所有数据居中对齐...")

        # 修复：将列数从9改为12，以匹配实际写入的数据列数
        cols = 12
        range_to_format = f"A1:L{total_rows}"
        
        try:
            format_data = {
                "horizontalAlignments": [["center"] * cols for _ in range(total_rows)],
                "verticalAlignments": [["middle"] * cols for _ in range(total_rows)]
            }
            
            success = self.dingtalk_sheet._apply_cell_format(
                sheet_id, range_to_format, format_data
            )

            if not success:
                # 获取更详细的错误信息
                last_error = getattr(self.dingtalk_sheet, 'last_error_response', '未知错误')
                print(f"⚠️ 居中对齐设置失败: {last_error}")

        except Exception as e:
            print(f"⚠️ 设置居中对齐时发生异常: {str(e)}")

    def merge_title_cells(self, sheet_id: str, monthly_summaries: Dict):
        """合并每个月度报告的标题行"""
        try:
            current_row = 1

            # 为每个月的标题合并单元格
            for month in sorted(monthly_summaries.keys()):
                summary = monthly_summaries[month]

                # 合并标题行 (A到L列)
                title_range = f"A{current_row}:L{current_row}"
                print(f"🔗 合并标题行: {title_range}")

                # 使用现有的合并单元格方法
                success = self.dingtalk_sheet._merge_cells(sheet_id, title_range, summary['month_display'])

                if success:
                    print(f"✅ 成功合并标题: {summary['month_display']}")
                else:
                    print(f"⚠️ 合并标题失败: {summary['month_display']}")

                # 计算下一个月标题的行号
                # 标题行(1) + 表头行(1) + 每日数据行数 + 空行(1)
                daily_data_count = len(summary.get('daily_data', []))
                current_row += 2 + daily_data_count + 1

        except Exception as e:
            print(f"⚠️ 合并单元格异常: {str(e)}")

    def run_complete_process(self):
        """运行完整的月度汇总流程"""
        try:
            # 获取工作表列表
            sheets = self.dingtalk_sheet.get_sheets_list()
            if not sheets:
                print("❌ 无法获取工作表列表")
                return False

            # 从配置文件中获取第一个工作表名称
            sheet1_name = self.config.get('dingtalk', {}).get('sheet', {}).get('sheet_name', '仓库绩效明细')

            # 查找匹配的工作表
            sheet1_id = None
            for sheet in sheets:
                if sheet.get('name') == sheet1_name:
                    sheet1_id = sheet.get('id')
                    break

            # 如果没找到匹配的名称，使用第一个工作表
            if not sheet1_id:
                sheet1_id = sheets[0].get('id', sheet1_name)
                print(f"⚠️ 未找到名为'{sheet1_name}'的工作表，使用第一个工作表")

            print(f"📋 使用{sheet1_name}: {sheet1_id}")

            # 确保Sheet2存在
            sheet2_name = self.config.get('dingtalk', {}).get('sheet', {}).get('sheet2_name', '仓库绩效汇总')

            # 查找匹配的工作表
            sheet2_id = None
            for sheet in sheets:
                if sheet.get('name') == sheet2_name:
                    sheet2_id = sheet.get('id')
                    break

            if not sheet2_id:
                print(f"⚠️ 未找到工作表 '{sheet2_name}'，正在尝试创建...")
                new_sheet_info = self.dingtalk_sheet.add_sheet(sheet2_name)
                if new_sheet_info:
                    sheet2_id = new_sheet_info.get('id')
                    print(f"✅ 成功创建工作表 '{sheet2_name}' (ID: {sheet2_id})")
                else:
                    print(f"❌ 创建工作表 '{sheet2_name}' 失败，中止汇总流程。")
                    return False
            
            # 读取Sheet1数据
            data_records = self.read_sheet1_data(sheet1_id)
            print(f"📊 读取到 {len(data_records)} 条有效数据记录")

            # 生成月度汇总
            monthly_summaries = self.generate_monthly_summaries(data_records)
            if not monthly_summaries:
                print("⚠️ 没有生成月度汇总数据")
                return False

            # 写入仓库绩效汇总
            return self.write_monthly_summaries_to_sheet2(monthly_summaries)

        except Exception as e:
            print(f"❌ 完整流程执行失败: {str(e)}")
            return False


def main():
    """主函数 - 测试读取仓库绩效明细数据"""
    print("🚀 开始测试读取仓库绩效明细数据...")
    
    # 创建月度汇总生成器
    generator = MonthlySummaryGenerator()
    
    if not generator.dingtalk_sheet:
        print("❌ 无法初始化钉钉表格工具，请检查配置")
        return
    
    # 获取工作表列表
    try:
        sheets = generator.dingtalk_sheet.get_sheets_list()
        if not sheets:
            print("❌ 无法获取工作表列表")
            return
        
        # 从配置文件中获取第一个工作表名称
        sheet1_name = generator.config.get('dingtalk', {}).get('sheet', {}).get('sheet_name', '仓库绩效明细')

        # 查找匹配的工作表
        sheet1_id = None
        for sheet in sheets:
            if sheet.get('name') == sheet1_name:
                sheet1_id = sheet.get('id')
                break

        # 如果没找到匹配的名称，使用第一个工作表
        if not sheet1_id:
            sheet1_id = sheets[0].get('id', sheet1_name)
            print(f"⚠️ 未找到名为'{sheet1_name}'的工作表，使用第一个工作表")

        print(f"📋 使用工作表: {sheet1_name} ({sheet1_id})")

        # 读取仓库绩效明细数据
        data_records = generator.read_sheet1_data(sheet1_id)
        
        if not data_records:
            print("⚠️ 没有读取到有效数据")
            return
        
        # 显示读取结果
        print(f"\n📊 数据读取结果:")
        print(f"总记录数: {len(data_records)}")
        
        # 显示前几条记录作为示例
        print(f"\n📋 前5条记录示例:")
        for i, record in enumerate(data_records[:5]):
            print(f"\n记录 {i+1}:")
            print(f"  时间: {record.get('timestamp', 'N/A')}")
            print(f"  待补货: 品数={record.get('待补货品数', 0)}, 订单={record.get('待补货订单数', 0)}")
            print(f"  待处理: 品数={record.get('待处理品数', 0)}, 订单={record.get('待处理订单数', 0)}")
            stock_weight = record.get('现货订单发货总重量', 0)
            custom_weight = record.get('定制订单发货总重量', 0)
            total_weight = record.get('发货总重量', 0)
            print(f"  发货: 现货={record.get('现货订单数', 0)}, 定制={record.get('定制订单数', 0)}")
            print(f"  重量: 现货{stock_weight}kg + 定制{custom_weight}kg = 总计{total_weight}kg")
            print(f"  已审核: 定制={record.get('已审核-定制', 0)}, 现货={record.get('已审核-现货', 0)}")
            print(f"  生产进度: {record.get('生产单完成进度', '')}")
            print(f"  奖惩: {record.get('奖惩金额', '0元')}")
        
        # 生成月度汇总
        print(f"\n📊 开始生成月度汇总...")
        monthly_summaries = generator.generate_monthly_summaries(data_records)

        print(f"\n📅 月度汇总结果:")
        for month in sorted(monthly_summaries.keys()):
            summary = monthly_summaries[month]
            print(f"\n🗓️ {summary['month_display']}")
            print(f"   数据天数: {summary['record_count']} 天")

            # 显示统计数据
            stats = summary.get('statistics', {})
            if stats:
                print(f"   📈 统计数据:")
                for field, data in stats.items():
                    if isinstance(data, dict):
                        avg = data.get('平均值', 'N/A')
                        max_val = data.get('最大值', 'N/A')
                        min_val = data.get('最小值', 'N/A')
                        total = data.get('总计', '')
                        total_str = f", 总计: {total}" if total else ""
                        print(f"     {field}: 平均 {avg}, 最大 {max_val}, 最小 {min_val}{total_str}")

            # 显示前3天的每日数据作为示例
            daily_data = summary.get('daily_data', [])
            if daily_data:
                print(f"   📋 每日数据示例 (前3天):")
                for i, day in enumerate(daily_data[:3]):
                    print(f"     {day['date']}: 待补货 {day['待补货品数']}/{day['待补货订单数']}, "
                          f"待处理 {day['待处理品数']}/{day['待处理订单数']}, "
                          f"发货 现货{day['现货订单数']}/定制{day['定制订单数']}/总重量{day['发货总重量']}kg, "
                          f"已审核 定制{day['已审核-定制']}/现货{day['已审核-现货']}, "
                          f"奖惩 {day['奖惩金额']}")

        print(f"\n✅ 月度汇总生成完成！")

        # 询问是否写入第二个工作表
        sheet2_name = generator.config.get('dingtalk', {}).get('sheet', {}).get('sheet2_name', '仓库绩效汇总')
        user_input = input(f"\n❓ 是否将月度汇总数据写入{sheet2_name}？(y/n): ").strip().lower()
        if user_input in ['y', 'yes', '是', '确定']:
            print(f"\n📝 开始写入{sheet2_name}...")
            success = generator.write_monthly_summaries_to_sheet2(monthly_summaries)
            if success:
                print(f"🎉 月度汇总数据已成功写入{sheet2_name}！")
            else:
                print(f"❌ 写入{sheet2_name}失败")
        else:
            print(f"💡 跳过写入{sheet2_name}，数据已生成完毕")

    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")


def run_auto_monthly_summary():
    """自动运行月度汇总流程（无需人工确认）"""
    print("🚀 开始自动运行月度汇总流程...")

    # 创建月度汇总生成器
    generator = MonthlySummaryGenerator()

    if not generator.dingtalk_sheet:
        print("❌ 无法初始化钉钉表格工具，请检查配置")
        return False

    try:
        # 获取工作表列表
        sheets = generator.dingtalk_sheet.get_sheets_list()
        if not sheets:
            print("❌ 无法获取工作表列表")
            return False

        # 从配置文件中获取第一个工作表名称
        sheet1_name = generator.config.get('dingtalk', {}).get('sheet', {}).get('sheet_name', '仓库绩效明细')

        # 查找匹配的工作表
        sheet1_id = None
        for sheet in sheets:
            if sheet.get('name') == sheet1_name:
                sheet1_id = sheet.get('id')
                break

        # 如果没找到匹配的名称，使用第一个工作表
        if not sheet1_id:
            sheet1_id = sheets[0].get('id', sheet1_name)
            print(f"⚠️ 未找到名为'{sheet1_name}'的工作表，使用第一个工作表")

        print(f"📋 使用工作表: {sheet1_name} ({sheet1_id})")

        # 读取仓库绩效明细数据
        data_records = generator.read_sheet1_data(sheet1_id)

        if not data_records:
            print("⚠️ 没有读取到有效数据，跳过月度汇总")
            return False

        print(f"📊 读取到 {len(data_records)} 条有效数据记录")

        # 生成月度汇总
        print(f"📊 开始生成月度汇总...")
        monthly_summaries = generator.generate_monthly_summaries(data_records)

        if not monthly_summaries:
            print("⚠️ 没有生成月度汇总数据")
            return False

        print(f"📅 生成了 {len(monthly_summaries)} 个月的汇总数据")

        # 自动写入Sheet2
        print(f"📝 开始写入Sheet2...")
        success = generator.write_monthly_summaries_to_sheet2(monthly_summaries)
        if success:
            print(f"🎉 月度汇总数据已成功写入Sheet2！")
            return True
        else:
            print(f"❌ 写入Sheet2失败")
            return False

    except Exception as e:
        print(f"❌ 自动月度汇总过程中出错: {str(e)}")
        return False

    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")


def run_complete_process():
    """运行完整的月度汇总流程（直接写入Sheet2）"""
    print("🚀 开始运行完整的月度汇总流程...")

    generator = MonthlySummaryGenerator()
    if not generator.dingtalk_sheet:
        print("❌ 无法初始化钉钉表格工具，请检查配置")
        return

    success = generator.run_complete_process()
    if success:
        print("🎉 月度汇总流程完成！数据已写入Sheet2")
    else:
        print("❌ 月度汇总流程失败")


if __name__ == "__main__":
    main()
