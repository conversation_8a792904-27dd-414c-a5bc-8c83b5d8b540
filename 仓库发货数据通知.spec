# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['warehouse_report.py'],
    pathex=[],
    binaries=[],
    datas=[('config.json', '.')],
    hiddenimports=['dingtalk_utils', 'dingtalk_sheet_utils', 'excel_processor', 'logging_utils', 'monthly_summary_generator', 'openpyxl.cell._writer', 'openpyxl.worksheet._writer'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['matplotlib', 'matplotlib.pyplot', 'matplotlib.backends', 'scipy', 'scipy.stats', 'scipy.sparse', 'scipy.linalg', 'scipy.optimize', 'sklearn', 'torch', 'tensorflow', 'seaborn', 'plotly', 'bokeh', 'altair', 'tkinter', 'PyQt5', 'PyQt6', 'PySide2', 'PySide6', 'PIL.ImageTk', 'IPython', 'jupyter', 'notebook', 'pytest', 'unittest', 'doctest', 'pdb', 'cProfile', 'profile', 'pandas.plotting', 'pandas.io.formats.style', 'pandas.io.parquet', 'pandas.io.sql', 'pandas.io.gbq', 'pandas.io.sas', 'pandas.io.spss', 'pandas.io.stata', 'pandas.io.feather', 'pandas.io.orc', 'sqlite3', 'pymongo', 'psycopg2', 'mysql', 'sqlalchemy', 'boto3', 'azure', 'google.cloud', 'paramiko', 'fabric', 'cv2', 'PIL.ImageQt', 'pygame'],
    noarchive=False,
    optimize=2,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [('O', None, 'OPTION'), ('O', None, 'OPTION')],
    name='仓库发货数据通知',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir='.',
    console=True,
    disable_windowed_traceback=True,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
