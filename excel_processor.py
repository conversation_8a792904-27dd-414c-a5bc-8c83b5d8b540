#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel发货数据处理模块
用于读取包含"销售出库单"关键词的Excel文件并汇总订单信息
"""

import os
import pandas as pd
import glob
from typing import Dict, List, Optional
from datetime import datetime


class ExcelProcessor:
    def __init__(self, config: dict):
        """
        初始化Excel处理器
        
        Args:
            config: 从config.json加载的配置字典
        """
        self.config = config
        self.data_directory = self.config.get("data_directory", ".")
        self.keywords = self.config.get("file_keywords", {})
        self.supported_extensions = ['.xlsx', '.xls']
        print(f"✅ Excel处理器初始化完成. 数据目录: {self.data_directory}")
        
    def _find_files_by_keyword(self, keyword: str, latest_only: bool = False) -> List[str]:
        """
        根据关键词在指定目录中查找文件
        
        Args:
            keyword (str): 文件名中包含的关键词
            latest_only (bool): 是否只返回修改时间最新的一个文件
            
        Returns:
            List[str]: 符合条件的文件路径列表
        """
        if not keyword:
            print(f"⚠️ 警告: 配置文件中未提供关键词，跳过搜索。")
            return []
            
        if not os.path.exists(self.data_directory):
            print(f"❌ 错误: 配置的数据目录不存在: {self.data_directory}")
            return []
        
        all_found_files = []
        for ext in self.supported_extensions:
            pattern = os.path.join(self.data_directory, f"*{keyword}*{ext}")
            # 使用glob查找文件
            found = glob.glob(pattern)
            # 过滤掉Excel临时文件 (以~$开头)
            filtered_files = [f for f in found if not os.path.basename(f).startswith('~$')]
            all_found_files.extend(filtered_files)

        if not all_found_files:
            print(f"⚠️ 在目录 '{self.data_directory}' 中未找到包含'{keyword}'关键词的Excel文件")
            return []
        
        if latest_only:
            latest_file = max(all_found_files, key=os.path.getmtime)
            print(f"✅ 找到最新的 '{keyword}' 文件: {os.path.basename(latest_file)}")
            return [latest_file]
        else:
            print(f"✅ 找到 {len(all_found_files)} 个 '{keyword}' 文件.")
            return sorted(all_found_files) # 排序以保证处理顺序一致

    def read_excel_file(self, file_path: str) -> Optional[pd.DataFrame]:
        """
        读取Excel文件
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            DataFrame或None
        """
        try:
            df = pd.read_excel(file_path, engine='openpyxl')
            print(f"   - 成功读取文件: {os.path.basename(file_path)}")
            return df
        except Exception as e:
            print(f"   - ❌ 读取文件失败 {os.path.basename(file_path)}: {str(e)}")
            return None
    
    def extract_shipping_data(self, df: pd.DataFrame) -> Dict[str, any]:
        """
        从DataFrame中提取发货相关数据
        
        Args:
            df: 包含发货数据的DataFrame
            
        Returns:
            包含汇总信息的字典
        """
        result = {"订单数": 0, "发货总金额": 0.0, "详情": "数据提取成功"}
        try:
            order_col = next((c for c in df.columns if "订单号" in str(c)), None)
            amount_col = next((c for c in df.columns if "应收金额" in str(c)), None)
            
            if order_col:
                valid_orders = df[order_col].dropna()
                result["订单数"] = len(valid_orders.unique())
            if amount_col:
                amounts = pd.to_numeric(df[amount_col], errors='coerce').dropna()
                result["发货总金额"] = round(amounts.sum(), 2)
        except Exception as e:
            result["详情"] = f"数据提取失败: {str(e)}"
        return result
    
    def process_all_shipping_files_new(self) -> Dict[str, any]:
        """
        处理所有销售出库单文件并按现货/定制分类汇总数据
        """
        print("\n📊 开始处理销售出库单文件...")

        # 查找现货和定制文件
        stock_files = self._find_files_by_keyword("销售出库单_现货")
        custom_files = self._find_files_by_keyword("销售出库单_定制")

        result = {
            "现货订单数": 0,
            "定制订单数": 0,
            "现货订单发货总重量": 0.0,
            "定制订单发货总重量": 0.0,
            "详情": "数据提取成功"
        }

        # 处理现货文件
        if stock_files:
            print(f"   📦 处理现货文件: {len(stock_files)} 个")
            for file_path in stock_files:
                df = self.read_excel_file(file_path)
                if df is not None:
                    file_result = self.extract_shipping_data_new(df, "现货")
                    result["现货订单数"] += file_result["订单数"]
                    result["现货订单发货总重量"] += file_result["重量"]
        else:
            print("   ⚠️ 未找到现货销售出库单文件")

        # 处理定制文件
        if custom_files:
            print(f"   🔧 处理定制文件: {len(custom_files)} 个")
            for file_path in custom_files:
                df = self.read_excel_file(file_path)
                if df is not None:
                    file_result = self.extract_shipping_data_new(df, "定制")
                    result["定制订单数"] += file_result["订单数"]
                    result["定制订单发货总重量"] += file_result["重量"]
        else:
            print("   ⚠️ 未找到定制销售出库单文件")

        result["现货订单发货总重量"] = round(result["现货订单发货总重量"], 2)
        result["定制订单发货总重量"] = round(result["定制订单发货总重量"], 2)
        print(f"   => 汇总结果: 现货订单={result['现货订单数']}, 现货重量={result['现货订单发货总重量']:.2f}kg, 定制订单={result['定制订单数']}, 定制重量={result['定制订单发货总重量']:.2f}kg")
        return result

    def process_all_shipping_files(self) -> Dict[str, any]:
        """
        处理所有销售出库单文件并汇总（旧版本，保持兼容性）
        """
        print("\n📊 开始处理销售出库单文件...")
        keyword = self.keywords.get("shipping_reports")
        shipping_files = self._find_files_by_keyword(keyword)

        if not shipping_files:
            return {"订单数": 0, "发货总金额": 0.0, "详情": "未找到销售出库单文件"}

        total_result = {"订单数": 0, "发货总金额": 0.0}
        for file_path in shipping_files:
            df = self.read_excel_file(file_path)
            if df is not None:
                file_result = self.extract_shipping_data(df)
                total_result["订单数"] += file_result["订单数"]
                total_result["发货总金额"] += file_result["发货总金额"]

        total_result["发货总金额"] = round(total_result["发货总金额"], 2)
        print(f"   => 汇总结果: 总订单数={total_result['订单数']}, 总金额={total_result['发货总金额']:.2f}")
        return total_result

    def extract_shipping_data_new(self, df: pd.DataFrame, file_type: str) -> Dict[str, any]:
        """
        从DataFrame中提取发货相关数据（新版本：订单数+重量）

        Args:
            df: 包含发货数据的DataFrame
            file_type: 文件类型（"现货"或"定制"）

        Returns:
            包含订单数和重量的字典
        """
        result = {"订单数": 0, "重量": 0.0, "详情": "数据提取成功"}
        try:
            # 查找订单号列
            order_col = next((c for c in df.columns if "订单号" in str(c)), None)

            # 查找称重结果列
            weight_col = next((c for c in df.columns if "称重结果" in str(c)), None)

            if order_col:
                valid_orders = df[order_col].dropna()
                result["订单数"] = len(valid_orders.unique())
                print(f"     - {file_type}订单数: {result['订单数']}")
            else:
                print(f"     - ⚠️ 未找到订单号列")

            if weight_col:
                weights = pd.to_numeric(df[weight_col], errors='coerce').dropna()
                result["重量"] = round(weights.sum(), 2)
                print(f"     - {file_type}重量: {result['重量']}kg")
            else:
                print(f"     - ⚠️ 未找到称重结果列")

        except Exception as e:
            result["详情"] = f"数据提取失败: {str(e)}"
            print(f"     - ❌ {file_type}数据提取失败: {str(e)}")

        return result

    def process_pending_orders_file(self) -> List[str]:
        """
        处理最新的待处理订单文件，返回订单号列表。
        """
        print("\n📊 开始处理待处理订单文件...")
        keyword = self.keywords.get("pending_orders")
        pending_files = self._find_files_by_keyword(keyword, latest_only=True)

        if not pending_files:
            return []

        df = self.read_excel_file(pending_files[0])
        if df is None or df.empty:
            return []
        
        order_col_name = "订单号"
        if order_col_name in df.columns:
            order_numbers = df[order_col_name].dropna().astype(str).apply(
                lambda x: x.split('.')[0] if x.endswith('.0') else x
            ).tolist()

            # 过滤掉包含"总计"的行，避免在订单列表中显示重复的总计标签
            filtered_orders = [order for order in order_numbers if "总计" not in str(order)]

            if len(filtered_orders) != len(order_numbers):
                print(f"   => 过滤掉 {len(order_numbers) - len(filtered_orders)} 个包含'总计'的行")

            print(f"   => 成功提取到 {len(filtered_orders)} 个待处理订单号")
            return filtered_orders
        else:
            print(f"   - ❌ 在文件中未找到 '{order_col_name}' 列")
            return []

    def process_warehouse_status_data(self, target_date: Optional[datetime] = None) -> Optional[Dict[str, any]]:
        """
        处理最新的仓库状态数据文件
        - 读取 "各部门绩效数据.xlsx" 文件
        - 查找特定日期的数据行
        - 如果是当天运行且没找到数据，则尝试查找前一天23点后的数据
        
        Args:
            target_date: 如果提供，则处理指定日期的数据；否则处理最新的数据
            
        Returns:
            包含所有KPI数据的字典，如果找不到则返回None
        """
        print("\n📊 开始处理最新的仓库状态数据文件...")
        keyword = self.keywords.get("warehouse_status")
        files = self._find_files_by_keyword(keyword, latest_only=True)

        if not files:
            return None

        file_path = files[0]
        try:
            # 尝试使用 openpyxl，如果失败则尝试 xlrd
            try:
                df = pd.read_excel(file_path, sheet_name="义乌仓", engine='openpyxl')
                print(f"   - 成功读取 '{os.path.basename(file_path)}' 的 '义乌仓' sheet页")
            except Exception as e_openpyxl:
                print(f"   - ⚠️ 使用openpyxl读取失败: {e_openpyxl}, 尝试使用xlrd...")
                df = pd.read_excel(file_path, sheet_name="义乌仓", engine='xlrd')
                print(f"   - 成功使用xlrd读取 '{os.path.basename(file_path)}' 的 '义乌仓' sheet页")

            # --- 核心数据查找逻辑 ---
            now = datetime.now()
            is_manual_run = target_date is not None
            day_to_find = target_date if is_manual_run else now
            
            # 查找目标日期的最新数据
            found_row = self._find_latest_record_for_date(df, day_to_find, file_path=file_path)

            # 如果是常规运行、且当天没找到数据，则尝试找前一天的
            if not found_row and not is_manual_run:
                print(f"   - ℹ️ 在Excel中未找到今天 ({day_to_find.strftime('%Y-%m-%d')}) 的任何记录, 尝试查找昨天的数据...")

                yesterday = day_to_find - pd.Timedelta(days=1)
                found_row = self._find_latest_record_for_date(df, yesterday, after_hour=23, file_path=file_path)
                
                if found_row:
                    print(f"   - ✅ 成功找到昨天晚上23点后的数据作为补充。")

            if not found_row:
                if not is_manual_run:
                    print(f"   - ℹ️ 在Excel中未找到今天 ({day_to_find.strftime('%Y-%m-%d')}) 或昨天23点后的任何记录。")
                else:
                    print(f"   - ℹ️ 在Excel中未找到指定日期 ({day_to_find.strftime('%Y-%m-%d')}) 的任何记录。")
                return None
            
            # --- 数据提取 ---
            print("   - ✅ 找到匹配的数据行，开始提取...")
            data = self._extract_data_from_row(found_row)
            
            # 将提取的时间戳格式化为字符串
            record_time = found_row.get('时间')
            if pd.notna(record_time):
                # 确保是datetime对象
                if isinstance(record_time, pd.Timestamp):
                    record_time_dt = record_time.to_pydatetime()
                else:
                    record_time_dt = record_time

                # 将找到的数据行的日期作为报告日期
                data['report_date'] = record_time_dt.strftime('%Y/%m/%d')
                data['report_timestamp'] = record_time_dt.strftime('%Y-%m-%d %H:%M:%S')
                print(f"   - 提取到数据，报告日期为: {data['report_date']}, 数据时间戳: {data['report_timestamp']}")
            else:
                # 如果时间列为空，则使用查找的日期
                data['report_date'] = day_to_find.strftime('%Y/%m/%d')
                data['report_timestamp'] = day_to_find.strftime('%Y-%m-%d %H:%M:%S')
                print(f"   - ⚠️ 时间列为空, 使用当前查找日期: {data['report_date']}")

            return data

        except FileNotFoundError:
            print(f"   - ❌ 文件未找到: {file_path}")
            return None
        except Exception as e:
            print(f"   - ❌ 处理文件 '{os.path.basename(file_path)}' 时出错: {str(e)}")
            return None

    def _find_latest_record_for_date(self, df: pd.DataFrame, target_date: datetime, after_hour: Optional[int] = None, file_path: str = None) -> Optional[Dict]:
        """
        在DataFrame中查找指定日期的最新一条记录。
        
        Args:
            df: 数据帧
            target_date: 目标日期
            after_hour: (可选) 如果提供，则只查找该小时之后的数据
            
        Returns:
            包含数据行的字典或None
        """
        # 查找时间列，兼容 '时间' 和 '日期'
        time_col = next((c for c in df.columns if "时间" in str(c) or "日期" in str(c)), None)
        if not time_col:
            print("   - ❌ 在Excel中未找到包含'时间'或'日期'的列")
            return None

        # 增加健壮性：清理列名中的前后空格
        df.columns = df.columns.str.strip()
        time_col = time_col.strip() # 确保我们使用的列名也是清理过的

        # 转换时间列为datetime对象，无法转换的设为NaT
        # 尝试多种格式，包括中文日期格式
        try:
            # 首先尝试标准解析
            df[time_col] = pd.to_datetime(df[time_col], errors='coerce')

            # 如果还有无法解析的，尝试中文格式
            mask = df[time_col].isna()
            if mask.any():
                print(f"   - 🔄 发现 {mask.sum()} 个无法解析的日期，尝试中文格式解析...")

                # 先保存原始数据用于调试
                original_col = time_col + '_original'
                if original_col not in df.columns:
                    # 从原始数据重新读取，避免被之前的转换影响
                    try:
                        print(f"   - 🔄 重新读取原始Excel文件以获取未转换的日期数据...")
                        df_original = pd.read_excel(file_path, sheet_name="义乌仓", engine='openpyxl')
                        df_original.columns = df_original.columns.str.strip()
                        if time_col in df_original.columns:
                            df[original_col] = df_original[time_col]
                            print(f"   - 📋 原始数据类型: {type(df_original[time_col].iloc[0])}")
                        else:
                            print(f"   - ❌ 在原始文件中未找到列 '{time_col}'")
                            df[original_col] = df[time_col]
                    except Exception as e:
                        print(f"   - ⚠️ 重新读取原始文件失败: {e}")
                        df[original_col] = df[time_col]

                # 显示前几个无法解析的样本用于调试
                sample_values = df[mask][original_col].head(3).tolist()
                print(f"   - 📋 无法解析的日期样本: {sample_values}")

                # 尝试解析中文日期格式
                success_count = 0
                for idx in df[mask].index:
                    original_value = df.loc[idx, original_col]
                    if pd.isna(original_value) or str(original_value).strip() in ['nan', 'NaT', '']:
                        continue

                    try:
                        date_str = str(original_value).strip()
                        parsed_date = None

                        # 格式1: 2025年7月17日 14:16:08
                        if '年' in date_str and '月' in date_str and '日' in date_str:
                            try:
                                # 尝试带时间的格式
                                parsed_date = pd.to_datetime(date_str, format='%Y年%m月%d日 %H:%M:%S', errors='coerce')
                                if pd.isna(parsed_date):
                                    # 尝试只有日期的格式
                                    parsed_date = pd.to_datetime(date_str, format='%Y年%m月%d日', errors='coerce')
                                if pd.isna(parsed_date):
                                    # 尝试自动解析
                                    parsed_date = pd.to_datetime(date_str, errors='coerce')
                            except:
                                pass

                        if parsed_date and not pd.isna(parsed_date):
                            df.loc[idx, time_col] = parsed_date
                            success_count += 1

                    except Exception as e:
                        continue

                print(f"   - ✅ 成功解析 {success_count} 个中文日期格式")

                # 再次检查还有多少无法解析的
                remaining_mask = df[time_col].isna()
                if remaining_mask.any():
                    remaining_samples = df[remaining_mask][original_col].head(2).tolist()
                    print(f"   - ⚠️ 仍有 {remaining_mask.sum()} 个日期无法解析，样本: {remaining_samples}")

        except Exception as e:
             print(f"   - ⚠️ 解析日期列 '{time_col}' 失败，请检查数据格式: {e}")
             return None
        
        # 筛选出非空的日期
        df_valid_time = df.dropna(subset=[time_col])

        # 筛选出目标日期的数据
        df_date_filtered = df_valid_time[df_valid_time[time_col].dt.date == target_date.date()]
        
        # 如果指定了小时，进一步筛选
        if after_hour is not None:
            df_date_filtered = df_date_filtered[df_date_filtered[time_col].dt.hour >= after_hour]

        if df_date_filtered.empty:
            return None
        
        # 按时间降序排序并取第一行
        latest_record = df_date_filtered.sort_values(by=time_col, ascending=False).iloc[0]

        # 重命名 '时间' 或 '日期' 列为 '时间'，方便后续提取
        latest_dict = latest_record.to_dict()
        if time_col != '时间' and time_col in latest_dict:
            latest_dict['时间'] = latest_dict.pop(time_col)
            
        return latest_dict

    def _extract_data_from_row(self, row_data: Dict) -> Dict[str, any]:
        """
        从数据行字典中提取所有需要的KPI指标。
        """
        data = {}

        # 辅助函数，安全地从行中获取数据
        def get_value(col_keyword, default=0):
            # 查找列名
            col_name = next((c for c in row_data.keys() if col_keyword in str(c)), None)
            if col_name:
                value = row_data[col_name]
                # 如果值是数字类型但为NaN，则返回默认值
                if pd.isna(value):
                    return default
                # 尝试将值转换为整数，如果失败则返回默认值
                try:
                    return int(value)
                except (ValueError, TypeError):
                    return default
            return default

        # 提取仓库状态数据 - 使用灵活的列名匹配
        def find_column_name(keywords):
            """根据关键词列表查找匹配的列名"""
            for keyword in keywords:
                for col in row_data.keys():
                    if keyword in str(col):
                        return col
            return None

        # 待补货数据
        stock_product_col = find_column_name(['待补货-品数', '待补货（需补货品数）', '待补货品数'])
        stock_order_col = find_column_name(['待补货-订单数', '待补货（订单数）', '待补货订单数'])

        data['待补货'] = {
            '品数': get_value(stock_product_col) if stock_product_col else 0,
            '订单数': get_value(stock_order_col) if stock_order_col else 0
        }

        # 待处理数据
        process_product_col = find_column_name(['待处理-品数', '待处理（料齐）', '待处理品数'])
        process_order_col = find_column_name(['待处理-订单数', '待处理（订单数）', '待处理订单数'])

        data['待处理'] = {
            '品数': get_value(process_product_col) if process_product_col else 0,
            '订单数': get_value(process_order_col) if process_order_col else 0
        }
        # 生产单数据
        unfinished_col = find_column_name(['生产单未完成单数', '生产单完成进度', '未完成单数'])
        total_production_col = find_column_name(['总生产单数', '生产单总数'])

        data['生产单未完成单数'] = {
            '总数': get_value(unfinished_col) if unfinished_col else 0
        }
        data['总生产单数'] = {
            '总数': get_value(total_production_col) if total_production_col else 0
        }

        # 已审核数据
        audited_custom_col = find_column_name(['已审核定制', '已审核-定制'])
        audited_stock_col = find_column_name(['已审核现货', '已审核-现货'])

        data['已审核-定制'] = {
            '订单数': get_value(audited_custom_col) if audited_custom_col else 0
        }
        data['已审核-现货'] = {
            '订单数': get_value(audited_stock_col) if audited_stock_col else 0
        }

        # 提取发货数据 (新版，直接从同一行读取)
        stock_order_col = find_column_name(['现货订单', '现货订单数'])
        custom_order_col = find_column_name(['定制订单', '定制订单数'])

        data['现货订单数'] = get_value(stock_order_col) if stock_order_col else 0
        data['定制订单数'] = get_value(custom_order_col) if custom_order_col else 0
        
        # 发货重量需要特殊处理，因为可能是浮点数
        stock_weight_col = find_column_name(['现货订单发货总重量', '现货发货重量', '现货重量'])
        if stock_weight_col and pd.notna(row_data[stock_weight_col]):
            try:
                data['现货订单发货总重量'] = round(float(row_data[stock_weight_col]), 2)
            except (ValueError, TypeError):
                data['现货订单发货总重量'] = 0.0
        else:
            data['现货订单发货总重量'] = 0.0

        custom_weight_col = find_column_name(['定制订单发货总重量', '定制发货重量', '定制重量'])
        if custom_weight_col and pd.notna(row_data[custom_weight_col]):
            try:
                data['定制订单发货总重量'] = round(float(row_data[custom_weight_col]), 2)
            except (ValueError, TypeError):
                data['定制订单发货总重量'] = 0.0
        else:
            data['定制订单发货总重量'] = 0.0
            
        return data 