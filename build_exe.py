#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仓库报告程序打包脚本 (重写版)
使用更直接的命令行方式调用PyInstaller，确保稳定性和单文件输出。
"""

import PyInstaller.__main__
import os

# 基本配置
app_name = "仓库发货数据通知"
script_file = "warehouse_report.py"

# PyInstaller参数
pyinstaller_args = [
    '--noconfirm',
    '--log-level=INFO',
    '--clean',
    '--onefile',
    f'--name={app_name}',
    # 添加config.json到包中
    '--add-data=config.json;.',
    # 添加logging_utils.py到包中
    '--add-data=logging_utils.py;.',
    script_file,
]

# 检查是否有图标文件，如果有则添加
icon_file = "assets/icon.ico" 
if os.path.exists(icon_file):
    pyinstaller_args.insert(-1, f'--icon={icon_file}')


if __name__ == '__main__':
    # 执行PyInstaller
    PyInstaller.__main__.run([
        'warehouse_report.py',
        '--onefile',
        '--name=仓库发货数据通知',
        '--hidden-import=dingtalk_utils',
        '--hidden-import=dingtalk_sheet_utils',
        '--hidden-import=excel_processor',
        '--hidden-import=logging_utils',
        '--hidden-import=monthly_summary_generator',
        # 添加 openpyxl 的相关 hidden-import
        '--hidden-import=openpyxl.cell._writer',
        '--hidden-import=openpyxl.worksheet._writer',
        '--add-data=config.json;.'
    ]) 