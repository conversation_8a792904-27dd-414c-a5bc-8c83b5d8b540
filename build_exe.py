#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仓库报告程序打包脚本 (优化版)
使用PyInstaller打包，排除不必要的大型库以减小exe体积
"""

import PyInstaller.__main__
import os

# 基本配置
app_name = "仓库发货数据通知"
script_file = "warehouse_report.py"

if __name__ == '__main__':
    print("🚀 开始优化打包，排除不必要的大型库...")

    # 检查是否有图标文件
    icon_args = []
    icon_file = "assets/icon.ico"
    if os.path.exists(icon_file):
        icon_args = [f'--icon={icon_file}']
        print(f"📎 找到图标文件: {icon_file}")

    # 执行PyInstaller - 优化版配置
    PyInstaller.__main__.run([
        script_file,
        '--onefile',
        '--noconfirm',
        '--clean',
        f'--name={app_name}',

        # 必需的隐藏导入
        '--hidden-import=dingtalk_utils',
        '--hidden-import=dingtalk_sheet_utils',
        '--hidden-import=excel_processor',
        '--hidden-import=logging_utils',
        '--hidden-import=monthly_summary_generator',
        '--hidden-import=openpyxl.cell._writer',
        '--hidden-import=openpyxl.worksheet._writer',

        # 添加必需的数据文件
        '--add-data=config.json;.',

        # 排除不必要的大型库 - 这是关键优化
        # 科学计算和可视化库（项目不需要）
        '--exclude-module=matplotlib',
        '--exclude-module=matplotlib.pyplot',
        '--exclude-module=matplotlib.backends',
        '--exclude-module=scipy',
        '--exclude-module=scipy.stats',
        '--exclude-module=scipy.sparse',
        '--exclude-module=scipy.linalg',
        '--exclude-module=scipy.optimize',
        '--exclude-module=sklearn',
        '--exclude-module=torch',
        '--exclude-module=tensorflow',
        '--exclude-module=seaborn',
        '--exclude-module=plotly',
        '--exclude-module=bokeh',
        '--exclude-module=altair',

        # GUI框架（项目是命令行程序）
        '--exclude-module=tkinter',
        '--exclude-module=PyQt5',
        '--exclude-module=PyQt6',
        '--exclude-module=PySide2',
        '--exclude-module=PySide6',
        '--exclude-module=PIL.ImageTk',

        # 开发和调试工具
        '--exclude-module=IPython',
        '--exclude-module=jupyter',
        '--exclude-module=notebook',
        '--exclude-module=pytest',
        '--exclude-module=unittest',
        '--exclude-module=doctest',
        '--exclude-module=pdb',
        '--exclude-module=cProfile',
        '--exclude-module=profile',

        # pandas的可选组件（减少pandas体积）
        '--exclude-module=pandas.plotting',
        '--exclude-module=pandas.io.formats.style',
        '--exclude-module=pandas.io.parquet',
        '--exclude-module=pandas.io.sql',
        '--exclude-module=pandas.io.gbq',
        '--exclude-module=pandas.io.sas',
        '--exclude-module=pandas.io.spss',
        '--exclude-module=pandas.io.stata',
        '--exclude-module=pandas.io.feather',
        '--exclude-module=pandas.io.orc',

        # 数据库驱动（项目不使用数据库）
        '--exclude-module=sqlite3',
        '--exclude-module=pymongo',
        '--exclude-module=psycopg2',
        '--exclude-module=mysql',
        '--exclude-module=sqlalchemy',

        # 网络和云服务（项目只用基本HTTP）
        '--exclude-module=boto3',
        '--exclude-module=azure',
        '--exclude-module=google.cloud',
        '--exclude-module=paramiko',
        '--exclude-module=fabric',

        # 多媒体处理（项目不需要）
        '--exclude-module=cv2',
        '--exclude-module=PIL.ImageQt',
        '--exclude-module=pygame',

        # 优化设置
        '--optimize=2',              # 最高级别的Python字节码优化
        '--strip',                   # 去除调试符号
        '--noupx',                   # 禁用UPX（有时会导致问题）
        '--console',                 # 保持控制台窗口
        '--disable-windowed-traceback',  # 禁用窗口化错误追踪

        # 运行时优化
        '--runtime-tmpdir=.',        # 设置运行时临时目录

    ] + icon_args)

    print("✅ 打包完成！")
    print("\n📊 优化说明：")
    print("   🔬 排除了大型科学计算库：matplotlib、scipy、sklearn等")
    print("   🖥️ 排除了GUI框架：tkinter、PyQt、PySide等")
    print("   🧪 排除了开发测试工具：pytest、IPython、jupyter等")
    print("   📊 排除了pandas的可选组件：plotting、parquet、sql等")
    print("   🗄️ 排除了数据库驱动：sqlite3、mysql、mongodb等")
    print("   ☁️ 排除了云服务SDK：boto3、azure、google.cloud等")
    print("   ⚡ 启用了最高级别的字节码优化")
    print("   🔧 启用了符号去除和运行时优化")
    print("\n💡 预计exe体积将从200MB+减少到30-50MB（减少70-85%）")
    print("📁 生成的exe文件位于: dist/仓库发货数据通知.exe")