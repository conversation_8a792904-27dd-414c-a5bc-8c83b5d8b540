2025-08-01 13:19:45 - INFO - 日志记录已初始化，stdout和stderr现已重定向到日志文件。
2025-08-01 13:19:45 - INFO - 🏭 仓库发货数据钉钉通知程序
2025-08-01 13:19:45 - INFO - ========================================
2025-08-01 13:19:45 - INFO - 🔑 正在获取钉钉access_token...
2025-08-01 13:19:45 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'oapi.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 13:19:46 - INFO - ✅ 获取access_token成功，有效期: 7200秒
2025-08-01 13:19:46 - INFO - 🔧 钉钉表格工具初始化 - 正式环境
2025-08-01 13:19:46 - INFO - 📋 表格ID: mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq
2025-08-01 13:19:46 - INFO - 📄 工作表1: 仓库绩效明细
2025-08-01 13:19:46 - INFO - 📄 工作表2: 仓库绩效汇总
2025-08-01 13:19:47 - INFO - ✅ 获取钉钉访问令牌成功
2025-08-01 13:19:47 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq
2025-08-01 13:19:47 - INFO - ✅ Excel处理器初始化完成. 数据目录: D:/仓库发货数据
2025-08-01 13:19:47 - INFO - 🚀 开始生成并发送仓库报告...
2025-08-01 13:19:47 - INFO - 🏭 开始处理最新的数据...
2025-08-01 13:19:47 - INFO - 📊 开始处理最新的仓库状态数据文件...
2025-08-01 13:19:47 - INFO - ✅ 找到最新的 '各部门绩效数据' 文件: 各部门绩效数据.xlsx
2025-08-01 13:19:47 - INFO - - 成功读取 '各部门绩效数据.xlsx' 的 '义乌仓' sheet页
2025-08-01 13:19:47 - ERROR - pandas\core\tools\datetimes.py:139: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
2025-08-01 13:19:47 - INFO - - 🔄 发现 72 个无法解析的日期，尝试中文格式解析...
2025-08-01 13:19:47 - INFO - - 🔄 重新读取原始Excel文件以获取未转换的日期数据...
2025-08-01 13:19:47 - INFO - - 📋 原始数据类型: <class 'str'>
2025-08-01 13:19:47 - INFO - - 📋 无法解析的日期样本: ['2025年06月27日 09:58:50', '2025年06月27日 10:14:11', '2025年06月28日 09:39:58']
2025-08-01 13:19:47 - INFO - - ✅ 成功解析 72 个中文日期格式
2025-08-01 13:19:47 - INFO - - ✅ 找到匹配的数据行，开始提取...
2025-08-01 13:19:47 - INFO - - 提取到数据，报告日期为: 2025/08/01, 数据时间戳: 2025-08-01 13:19:20
2025-08-01 13:19:47 - INFO - 📊 开始处理待处理订单文件...
2025-08-01 13:19:47 - INFO - ✅ 找到最新的 '待处理订单' 文件: 待处理订单.xlsx
2025-08-01 13:19:47 - ERROR - openpyxl\styles\stylesheet.py:237: UserWarning: Workbook contains no default style, apply openpyxl's default
2025-08-01 13:19:47 - INFO - - 成功读取文件: 待处理订单.xlsx
2025-08-01 13:19:47 - INFO - => 过滤掉 1 个包含'总计'的行
2025-08-01 13:19:47 - INFO - => 成功提取到 28 个待处理订单号
2025-08-01 13:19:47 - INFO - 数据写入阈值: 21点, 当前是否达到: 否
2025-08-01 13:19:47 - INFO - 📤 正在发送主报告...
2025-08-01 13:19:48 - INFO - ✅ 钉钉消息发送成功
2025-08-01 13:19:48 - INFO - ✅ 主报告发送成功！
2025-08-01 13:19:48 - INFO - 📊 奖惩计算基础数据：
2025-08-01 13:19:48 - INFO - 已审核-定制：111单
2025-08-01 13:19:48 - INFO - 已审核-现货：1510单
2025-08-01 13:19:48 - INFO - 已审核订单总计：1621单 (定制+现货)
2025-08-01 13:19:48 - INFO - 待补货订单：0单
2025-08-01 13:19:48 - INFO - 待处理订单：28单
2025-08-01 13:19:48 - INFO - --- 阶段三：开始生成月度汇总 ---
2025-08-01 13:19:48 - INFO - 🚀 开始自动运行月度汇总流程...
2025-08-01 13:19:48 - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 13:19:48 - INFO - 🔧 钉钉表格工具初始化 - 正式环境
2025-08-01 13:19:48 - INFO - 📋 表格ID: mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq
2025-08-01 13:19:48 - INFO - 📄 工作表1: 仓库绩效明细
2025-08-01 13:19:48 - INFO - 📄 工作表2: 仓库绩效汇总
2025-08-01 13:19:49 - INFO - ✅ 获取钉钉访问令牌成功
2025-08-01 13:19:49 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq
2025-08-01 13:19:49 - INFO - ✅ 钉钉表格工具初始化完成
2025-08-01 13:19:49 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 13:19:49 - INFO - 📋 使用工作表: 仓库绩效明细 (kgqie6hm)
2025-08-01 13:19:49 - INFO - 🔍 开始读取仓库绩效明细数据...
2025-08-01 13:19:49 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 13:19:50 - INFO - 📊 读取到 500 行原始数据
2025-08-01 13:19:50 - INFO - ✅ 成功解析 28 条有效数据记录
2025-08-01 13:19:50 - INFO - 📊 读取到 28 条有效数据记录
2025-08-01 13:19:50 - INFO - 📊 开始生成月度汇总...
2025-08-01 13:19:50 - INFO - 📅 生成了 1 个月的汇总数据
2025-08-01 13:19:50 - INFO - 📝 开始写入Sheet2...
2025-08-01 13:19:50 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 13:19:50 - INFO - 📋 开始写入仓库绩效汇总: st-8072ca1b-33873
2025-08-01 13:19:50 - INFO - 📝 准备写入数据...
2025-08-01 13:19:50 - INFO - 📝 准备 2025年07月数据汇总 数据...
2025-08-01 13:19:50 - INFO - 📊 开始写入 31 行数据到仓库绩效汇总...
2025-08-01 13:19:50 - INFO - 🔍 调试信息:
2025-08-01 13:19:50 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq/sheets/st-8072ca1b-33873/ranges/A1:L31?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-08-01 13:19:50 - INFO - 数据: [['2025年07月数据汇总', '', '', '', '', '', '', '', '', '', '', ''], ['日期', '待补货品数', '待补货订单', '待处理品数', '待处理订单', '现货订单', '定制订单', '发货重量', '已审核定制', '已审核现货', '生产进度', '奖惩金额'], ['07-02', '14', '10', '18', '16', '0', '0', '0', '0', '0', '', '+35元'], ['07-03', '2', '1', '19', '20', '0', '0', '0', '0', '0', '', '+35元'], ['07-04', '1', '1', '25', '23', '0', '0', '0', '0', '0', '', '+35元'], ['07-05', '1', '1', '11', '6', '0', '0', '0', '0', '0', '', '+35元'], ['07-06', '3', '1', '12', '7', '0', '0', '0', '0', '0', '', '+35元'], ['07-07', '1', '1', '8', '7', '2736', '124', '0', '179', '2727', '未完成：1/12，已完成：11/12', '+35元'], ['07-08', '9', '4', '4', '3', '2363', '199', '7499.98', '170', '2368', '未完成：7/16，已完成：9/16', '+35元'], ['07-09', '0', '0', '5', '4', '2978', '190', '8494.05', '160', '3017', '未完成：5/5，已完成：0/5', '+35元'], ['07-10', '0', '0', '20', '16', '2836', '169', '9193.89', '179', '2872', '未完成：0/12，已完成：12/12', '+35元'], ['07-11', '13', '11', '16', '22', '2598', '262', '8755.34', '120', '2599', '未完成：1/19，已完成：18/19', '+35元'], ['07-12', '10', '12', '13', '9', '2907', '139', '8269.630000000001', '192', '2869', '未完成：5/11，已完成：6/11', '+35元'], ['07-13', '7', '10', '18', '10', '2855', '170', '9088.82', '144', '2792', '未完成：3/26，已完成：23/26', '+35元'], ['07-16', '1', '1', '6', '6', '2731', '235', '8354.62', '205', '2730', '未完成：0/9，已完成：9/9', '+35元'], ['07-17', '0', '0', '0', '0', '2644', '186', '7829.78', '164', '2688', '未完成：5/13，已完成：8/13', '0元'], ['07-18', '0', '0', '1', '1', '2878', '239', '8752.17', '140', '2816', '未完成：1/10，已完成：9/10', '+35元'], ['07-19', '0', '0', '1', '1', '2828', '219', '8963.22', '195', '2875', '未完成：1/12，已完成：11/12', '+35元'], ['07-20', '0', '0', '7', '3', '2794', '218', '9405.55', '146', '2726', '未完成：3/14，已完成：11/14', '+35元'], ['07-21', '0', '0', '3', '2', '2615', '191', '8301.57', '139', '2476', '未完成：0/7，已完成：7/7', '+35元'], ['07-22', '0', '0', '0', '0', '2738', '171', '7482.150000000001', '208', '2787', '未完成：0/7，已完成：7/7', '+35元'], ['07-23', '0', '0', '4', '6', '2564', '181', '7340.33', '145', '2602', '未完成：4/8，已完成：4/8', '+35元'], ['07-24', '0', '0', '2', '1', '2523', '190', '8716.119999999999', '122', '2537', '未完成：0/7，已完成：7/7', '+35元'], ['07-25', '0', '0', '3', '2', '2522', '177', '8057.49', '221', '2542', '未完成：0/9，已完成：9/9', '+35元'], ['07-26', '0', '0', '4', '3', '2405', '212', '6910.71', '152', '2254', '未完成：3/20，已完成：17/20', '+35元'], ['07-27', '0', '0', '9', '5', '2726', '169', '8653.66', '214', '2754', '未完成：5/15，已完成：10/15', '+35元'], ['07-28', '0', '0', '32', '27', '2541', '193', '7306.07', '167', '2456', '未完成：1/10，已完成：9/10', '-15元'], ['07-29', '0', '0', '10', '7', '2413', '175', '8981.220000000001', '152', '2368', '未完成：4/10，已完成：6/10', '+35元'], ['07-30', '0', '0', '13', '9', '2425', '189', '8532.55', '160', '2216', '未完成：2/11，已完成：9/11', '+35元'], ['07-31', '0', '0', '4', '2', '2628', '159', '5684.1', '161', '2609', '未完成：1/10，已完成：9/10', '0元'], ['', '', '', '', '', '', '', '', '', '', '', '']]
2025-08-01 13:19:50 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 13:19:51 - INFO - 响应状态码: 200
2025-08-01 13:19:51 - INFO - 响应内容: {"a1Notation":"A1:L31"}
2025-08-01 13:19:51 - INFO - ✅ 成功写入数据到工作表 st-8072ca1b-33873!A1:L31
2025-08-01 13:19:51 - INFO - ✅ 数据写入成功
2025-08-01 13:19:51 - INFO - 🎨 应用格式设置...
2025-08-01 13:19:51 - INFO - 📐 设置所有数据居中对齐...
2025-08-01 13:19:51 - INFO - 🔍 格式设置调试:
2025-08-01 13:19:51 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq/sheets/st-8072ca1b-33873/ranges/A1:L31?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-08-01 13:19:51 - INFO - 格式数据: {'horizontalAlignments': [['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center']], 'verticalAlignments': [['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle']]}
2025-08-01 13:19:52 - INFO - 响应状态码: 200
2025-08-01 13:19:52 - INFO - 响应内容: {"a1Notation":"A1:L31"}
2025-08-01 13:19:52 - INFO - 🔗 合并标题行: A1:L1
2025-08-01 13:19:52 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 13:19:52 - INFO - ✅ 成功合并单元格: A1:L1
2025-08-01 13:19:52 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 13:19:52 - INFO - ✅ 成功设置合并单元格的值和格式: A1 = '2025年07月数据汇总'
2025-08-01 13:19:52 - INFO - ✅ 成功合并标题: 2025年07月数据汇总
2025-08-01 13:19:52 - INFO - 🎉 月度汇总数据已成功写入Sheet2！
2025-08-01 13:19:52 - INFO - 🎉 报告流程处理完成.
2025-08-01 23:08:59 - INFO - 日志记录已初始化，stdout和stderr现已重定向到日志文件。
2025-08-01 23:08:59 - INFO - 🏭 仓库发货数据钉钉通知程序
2025-08-01 23:08:59 - INFO - ========================================
2025-08-01 23:08:59 - INFO - 🔑 正在获取钉钉access_token...
2025-08-01 23:09:00 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'oapi.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:00 - INFO - ✅ 获取access_token成功，有效期: 7200秒
2025-08-01 23:09:00 - INFO - 🔧 钉钉表格工具初始化 - 正式环境
2025-08-01 23:09:00 - INFO - 📋 表格ID: mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq
2025-08-01 23:09:00 - INFO - 📄 工作表1: 仓库绩效明细
2025-08-01 23:09:00 - INFO - 📄 工作表2: 仓库绩效汇总
2025-08-01 23:09:01 - INFO - ✅ 获取钉钉访问令牌成功
2025-08-01 23:09:01 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq
2025-08-01 23:09:01 - INFO - ✅ Excel处理器初始化完成. 数据目录: D:/仓库发货数据
2025-08-01 23:09:01 - INFO - 🚀 开始生成并发送仓库报告...
2025-08-01 23:09:01 - INFO - 🏭 开始处理最新的数据...
2025-08-01 23:09:01 - INFO - 📊 开始处理最新的仓库状态数据文件...
2025-08-01 23:09:01 - INFO - ✅ 找到最新的 '各部门绩效数据' 文件: 各部门绩效数据.xlsx
2025-08-01 23:09:01 - INFO - - 成功读取 '各部门绩效数据.xlsx' 的 '义乌仓' sheet页
2025-08-01 23:09:01 - ERROR - pandas\core\tools\datetimes.py:139: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
2025-08-01 23:09:01 - INFO - - 🔄 发现 73 个无法解析的日期，尝试中文格式解析...
2025-08-01 23:09:01 - INFO - - 🔄 重新读取原始Excel文件以获取未转换的日期数据...
2025-08-01 23:09:01 - INFO - - 📋 原始数据类型: <class 'str'>
2025-08-01 23:09:01 - INFO - - 📋 无法解析的日期样本: ['2025年06月27日 09:58:50', '2025年06月27日 10:14:11', '2025年06月28日 09:39:58']
2025-08-01 23:09:01 - INFO - - ✅ 成功解析 73 个中文日期格式
2025-08-01 23:09:01 - INFO - - ✅ 找到匹配的数据行，开始提取...
2025-08-01 23:09:01 - INFO - - 提取到数据，报告日期为: 2025/08/01, 数据时间戳: 2025-08-01 23:08:48
2025-08-01 23:09:01 - INFO - 📊 开始处理待处理订单文件...
2025-08-01 23:09:01 - INFO - ✅ 找到最新的 '待处理订单' 文件: 待处理订单.xlsx
2025-08-01 23:09:01 - ERROR - openpyxl\styles\stylesheet.py:237: UserWarning: Workbook contains no default style, apply openpyxl's default
2025-08-01 23:09:01 - INFO - - 成功读取文件: 待处理订单.xlsx
2025-08-01 23:09:01 - INFO - => 过滤掉 1 个包含'总计'的行
2025-08-01 23:09:01 - INFO - => 成功提取到 4 个待处理订单号
2025-08-01 23:09:01 - INFO - 数据写入阈值: 21点, 当前是否达到: 是
2025-08-01 23:09:01 - INFO - 📤 正在发送主报告...
2025-08-01 23:09:02 - INFO - ✅ 钉钉消息发送成功
2025-08-01 23:09:02 - INFO - ✅ 主报告发送成功！
2025-08-01 23:09:02 - INFO - 📤 正在分条发送待处理订单列表...
2025-08-01 23:09:02 - INFO - 📋 准备分条发送 4 个待处理订单，分为 1 条消息
2025-08-01 23:09:03 - INFO - ✅ 钉钉消息发送成功
2025-08-01 23:09:03 - INFO - ✅ 第 1/1 批待处理订单发送成功 (4 个订单)
2025-08-01 23:09:03 - INFO - 🎉 所有待处理订单列表发送完成！共发送 1 条消息，4 个订单
2025-08-01 23:09:03 - INFO - 📊 奖惩计算基础数据：
2025-08-01 23:09:03 - INFO - 已审核-定制：145单
2025-08-01 23:09:03 - INFO - 已审核-现货：2524单
2025-08-01 23:09:03 - INFO - 已审核订单总计：2669单 (定制+现货)
2025-08-01 23:09:03 - INFO - 待补货订单：0单
2025-08-01 23:09:03 - INFO - 待处理订单：4单
2025-08-01 23:09:04 - INFO - 📤 正在发送奖惩报告...
2025-08-01 23:09:04 - INFO - 📤 当前环境: 正式环境
2025-08-01 23:09:04 - INFO - 📤 发送目标: 宋继文（义乌仓储部） (userId: 1529202823554123)
2025-08-01 23:09:04 - INFO - 📤 使用机器人发送个人消息给用户 1529202823554123
2025-08-01 23:09:04 - INFO - 📋 消息标题: 奖惩计算结果
2025-08-01 23:09:04 - INFO - 📄 消息内容: 奖惩计算结果

基础数据：

已审核订单：2,669单

待补货订单：0单

待处理订单：4单

待补货奖惩（10元规则）：
条件：已审核≤4000且待补货≤15
结果：奖励 +10元

待处理奖惩（...
2025-08-01 23:09:04 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:04 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'oapi.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:04 - INFO - 📊 发送结果: {'flowControlledStaffIdList': [], 'invalidStaffIdList': [], 'processQueryKey': 'GjTB0FZ8HnMM6sj163sJZBnwbpQGW3WOrHsePQRziiM='}
2025-08-01 23:09:04 - INFO - ✅ 机器人消息发送成功
2025-08-01 23:09:05 - INFO - 任务查询键: GjTB0FZ8HnMM6sj163sJZBnwbpQGW3WOrHsePQRziiM=
2025-08-01 23:09:05 - INFO - ✅ 奖惩报告已发送给宋继文（义乌仓储部） - 正式环境
2025-08-01 23:09:05 - INFO - 📊 正在将报告块写入到钉钉表格...
2025-08-01 23:09:05 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:05 - INFO - 📝 检查日期为 2025/08/01 的数据是否已存在...
2025-08-01 23:09:05 - INFO - 🔍 正在表格中搜索日期为 '2025/08/01' 的记录...
2025-08-01 23:09:05 - INFO - - 优化扫描范围: B6:B506
2025-08-01 23:09:05 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:05 - INFO - ℹ️ 未找到日期为 '2025/08/01' 的记录。
2025-08-01 23:09:05 - INFO - 📍 使用配置文件中记录的最后一行: 506
2025-08-01 23:09:05 - INFO - 📍 找到最后数据行: 506，下一个数据块将从第 508 行开始（留出1行空行分隔）
2025-08-01 23:09:05 - INFO - 🔩 使用强制指定的开始行: 508
2025-08-01 23:09:05 - INFO - 🔍 调试信息:
2025-08-01 23:09:05 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq/sheets/kgqie6hm/ranges/A508:C525?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-08-01 23:09:05 - INFO - 数据: [['数据统计时间', '2025/08/01 23:09:05', ''], ['仓库状态数据', '', ''], ['类型', '品数', '订单数'], ['待补货', '0', '0'], ['待处理', '5', '4'], ['生产单完成进度', '', ''], ['已审核-定制', '', ''], ['已审核-现货', '', ''], ['发货数据', '', ''], ['现货订单数', '', ''], ['现货订单发货总重量', '', ''], ['定制订单数', '', ''], ['定制订单发货总重量', '', ''], ['奖惩计算', '', ''], ['待补货奖惩', '', ''], ['待处理奖惩', '', ''], ['总计奖惩', '', ''], ['奖惩计算结果', '', '']]
2025-08-01 23:09:06 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:06 - INFO - 响应状态码: 200
2025-08-01 23:09:06 - INFO - 响应内容: {"a1Notation":"A508:C525"}
2025-08-01 23:09:06 - INFO - ✅ 成功写入数据到工作表 kgqie6hm!A508:C525
2025-08-01 23:09:06 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:06 - INFO - ✅ 成功设置综合格式: A508:C525
2025-08-01 23:09:06 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:06 - INFO - ✅ 成功合并单元格: B508:C508
2025-08-01 23:09:06 - INFO - 🕐 检测到时间格式，设置为文本格式: 2025/08/01 23:09:05
2025-08-01 23:09:06 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:06 - INFO - ✅ 成功设置合并单元格的值和格式: B508 = '2025/08/01 23:09:05'
2025-08-01 23:09:06 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:07 - INFO - ✅ 成功合并单元格: A509:C509
2025-08-01 23:09:07 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:07 - INFO - ✅ 成功设置合并单元格的值和格式: A509 = '仓库状态数据'
2025-08-01 23:09:07 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:07 - INFO - ✅ 成功合并单元格: B513:C513
2025-08-01 23:09:07 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:07 - INFO - ✅ 成功设置合并单元格的值和格式: B513 = '未完成：0/7，已完成：7/7'
2025-08-01 23:09:07 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:08 - INFO - ✅ 成功合并单元格: B514:C514
2025-08-01 23:09:08 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:08 - INFO - ✅ 成功设置合并单元格的值和格式: B514 = '145'
2025-08-01 23:09:08 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:08 - INFO - ✅ 成功合并单元格: B515:C515
2025-08-01 23:09:08 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:08 - INFO - ✅ 成功设置合并单元格的值和格式: B515 = '2524'
2025-08-01 23:09:08 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:08 - INFO - ✅ 成功合并单元格: A516:C516
2025-08-01 23:09:09 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:09 - INFO - ✅ 成功设置合并单元格的值和格式: A516 = '发货数据'
2025-08-01 23:09:09 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:09 - INFO - ✅ 成功合并单元格: B517:C517
2025-08-01 23:09:09 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:09 - INFO - ✅ 成功设置合并单元格的值和格式: B517 = '2632'
2025-08-01 23:09:09 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:09 - INFO - ✅ 成功合并单元格: B518:C518
2025-08-01 23:09:09 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:10 - INFO - ✅ 成功设置合并单元格的值和格式: B518 = '4730.34kg'
2025-08-01 23:09:10 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:10 - INFO - ✅ 成功合并单元格: B519:C519
2025-08-01 23:09:10 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:10 - INFO - ✅ 成功设置合并单元格的值和格式: B519 = '219'
2025-08-01 23:09:10 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:10 - INFO - ✅ 成功合并单元格: B520:C520
2025-08-01 23:09:10 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:10 - INFO - ✅ 成功设置合并单元格的值和格式: B520 = '3316.21kg'
2025-08-01 23:09:10 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:11 - INFO - ✅ 成功合并单元格: A521:C521
2025-08-01 23:09:11 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:11 - INFO - ✅ 成功设置合并单元格的值和格式: A521 = '奖惩计算'
2025-08-01 23:09:11 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:11 - INFO - ✅ 成功合并单元格: B522:C522
2025-08-01 23:09:11 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:11 - INFO - ✅ 成功设置合并单元格的值和格式: B522 = '奖励 +10元'
2025-08-01 23:09:11 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:11 - INFO - ✅ 成功合并单元格: B523:C523
2025-08-01 23:09:12 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:12 - INFO - ✅ 成功设置合并单元格的值和格式: B523 = '奖励 +25元'
2025-08-01 23:09:12 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:12 - INFO - ✅ 成功合并单元格: B524:C524
2025-08-01 23:09:12 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:12 - INFO - ✅ 成功设置合并单元格的值和格式: B524 = '+35元'
2025-08-01 23:09:12 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:12 - INFO - ✅ 成功合并单元格: A525:C525
2025-08-01 23:09:12 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:13 - INFO - ✅ 成功设置合并单元格的值和格式: A525 = '已审核订单：2,669单 待补货订单：0单 待处理订单：4单 | 待补货奖惩（10元规则）：已审核≤4000且待补货≤15 结果：奖励 10元 | 待处理奖惩（25元规则）：已审核≤4000且待处理≤26（1%） 结果：奖励 25元 | 总计奖惩：+35元'
2025-08-01 23:09:13 - INFO - ℹ️ 钉钉表格API不支持边框设置，跳过边框设置: A508:C525
2025-08-01 23:09:13 - INFO - ✅ 成功追加仓库报告块到第 508-525 行
2025-08-01 23:09:13 - INFO - ✅ 数据成功写入钉钉表格！
2025-08-01 23:09:13 - INFO - 📝 已更新正式环境配置文件，记录最后一行: 525
2025-08-01 23:09:13 - INFO - --- 阶段三：开始生成月度汇总 ---
2025-08-01 23:09:13 - INFO - 🚀 开始自动运行月度汇总流程...
2025-08-01 23:09:13 - INFO - ✅ 配置文件加载成功: config.json
2025-08-01 23:09:13 - INFO - 🔧 钉钉表格工具初始化 - 正式环境
2025-08-01 23:09:13 - INFO - 📋 表格ID: mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq
2025-08-01 23:09:13 - INFO - 📄 工作表1: 仓库绩效明细
2025-08-01 23:09:13 - INFO - 📄 工作表2: 仓库绩效汇总
2025-08-01 23:09:13 - INFO - ✅ 获取钉钉访问令牌成功
2025-08-01 23:09:13 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq
2025-08-01 23:09:13 - INFO - ✅ 钉钉表格工具初始化完成
2025-08-01 23:09:14 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:14 - INFO - 📋 使用工作表: 仓库绩效明细 (kgqie6hm)
2025-08-01 23:09:14 - INFO - 🔍 开始读取仓库绩效明细数据...
2025-08-01 23:09:14 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:14 - INFO - 📊 读取到 500 行原始数据
2025-08-01 23:09:14 - INFO - ✅ 成功解析 28 条有效数据记录
2025-08-01 23:09:14 - INFO - 📊 读取到 28 条有效数据记录
2025-08-01 23:09:14 - INFO - 📊 开始生成月度汇总...
2025-08-01 23:09:14 - INFO - 📅 生成了 1 个月的汇总数据
2025-08-01 23:09:14 - INFO - 📝 开始写入Sheet2...
2025-08-01 23:09:14 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:15 - INFO - 📋 开始写入仓库绩效汇总: st-8072ca1b-33873
2025-08-01 23:09:15 - INFO - 📝 准备写入数据...
2025-08-01 23:09:15 - INFO - 📝 准备 2025年07月数据汇总 数据...
2025-08-01 23:09:15 - INFO - 📊 开始写入 31 行数据到仓库绩效汇总...
2025-08-01 23:09:15 - INFO - 🔍 调试信息:
2025-08-01 23:09:15 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq/sheets/st-8072ca1b-33873/ranges/A1:L31?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-08-01 23:09:15 - INFO - 数据: [['2025年07月数据汇总', '', '', '', '', '', '', '', '', '', '', ''], ['日期', '待补货品数', '待补货订单', '待处理品数', '待处理订单', '现货订单', '定制订单', '发货重量', '已审核定制', '已审核现货', '生产进度', '奖惩金额'], ['07-02', '14', '10', '18', '16', '0', '0', '0', '0', '0', '', '+35元'], ['07-03', '2', '1', '19', '20', '0', '0', '0', '0', '0', '', '+35元'], ['07-04', '1', '1', '25', '23', '0', '0', '0', '0', '0', '', '+35元'], ['07-05', '1', '1', '11', '6', '0', '0', '0', '0', '0', '', '+35元'], ['07-06', '3', '1', '12', '7', '0', '0', '0', '0', '0', '', '+35元'], ['07-07', '1', '1', '8', '7', '2736', '124', '0', '179', '2727', '未完成：1/12，已完成：11/12', '+35元'], ['07-08', '9', '4', '4', '3', '2363', '199', '7499.98', '170', '2368', '未完成：7/16，已完成：9/16', '+35元'], ['07-09', '0', '0', '5', '4', '2978', '190', '8494.05', '160', '3017', '未完成：5/5，已完成：0/5', '+35元'], ['07-10', '0', '0', '20', '16', '2836', '169', '9193.89', '179', '2872', '未完成：0/12，已完成：12/12', '+35元'], ['07-11', '13', '11', '16', '22', '2598', '262', '8755.34', '120', '2599', '未完成：1/19，已完成：18/19', '+35元'], ['07-12', '10', '12', '13', '9', '2907', '139', '8269.630000000001', '192', '2869', '未完成：5/11，已完成：6/11', '+35元'], ['07-13', '7', '10', '18', '10', '2855', '170', '9088.82', '144', '2792', '未完成：3/26，已完成：23/26', '+35元'], ['07-16', '1', '1', '6', '6', '2731', '235', '8354.62', '205', '2730', '未完成：0/9，已完成：9/9', '+35元'], ['07-17', '0', '0', '0', '0', '2644', '186', '7829.78', '164', '2688', '未完成：5/13，已完成：8/13', '0元'], ['07-18', '0', '0', '1', '1', '2878', '239', '8752.17', '140', '2816', '未完成：1/10，已完成：9/10', '+35元'], ['07-19', '0', '0', '1', '1', '2828', '219', '8963.22', '195', '2875', '未完成：1/12，已完成：11/12', '+35元'], ['07-20', '0', '0', '7', '3', '2794', '218', '9405.55', '146', '2726', '未完成：3/14，已完成：11/14', '+35元'], ['07-21', '0', '0', '3', '2', '2615', '191', '8301.57', '139', '2476', '未完成：0/7，已完成：7/7', '+35元'], ['07-22', '0', '0', '0', '0', '2738', '171', '7482.150000000001', '208', '2787', '未完成：0/7，已完成：7/7', '+35元'], ['07-23', '0', '0', '4', '6', '2564', '181', '7340.33', '145', '2602', '未完成：4/8，已完成：4/8', '+35元'], ['07-24', '0', '0', '2', '1', '2523', '190', '8716.119999999999', '122', '2537', '未完成：0/7，已完成：7/7', '+35元'], ['07-25', '0', '0', '3', '2', '2522', '177', '8057.49', '221', '2542', '未完成：0/9，已完成：9/9', '+35元'], ['07-26', '0', '0', '4', '3', '2405', '212', '6910.71', '152', '2254', '未完成：3/20，已完成：17/20', '+35元'], ['07-27', '0', '0', '9', '5', '2726', '169', '8653.66', '214', '2754', '未完成：5/15，已完成：10/15', '+35元'], ['07-28', '0', '0', '32', '27', '2541', '193', '7306.07', '167', '2456', '未完成：1/10，已完成：9/10', '-15元'], ['07-29', '0', '0', '10', '7', '2413', '175', '8981.220000000001', '152', '2368', '未完成：4/10，已完成：6/10', '+35元'], ['07-30', '0', '0', '13', '9', '2425', '189', '8532.55', '160', '2216', '未完成：2/11，已完成：9/11', '+35元'], ['07-31', '0', '0', '4', '2', '2628', '159', '5684.1', '161', '2609', '未完成：1/10，已完成：9/10', '0元'], ['', '', '', '', '', '', '', '', '', '', '', '']]
2025-08-01 23:09:15 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:15 - INFO - 响应状态码: 200
2025-08-01 23:09:15 - INFO - 响应内容: {"a1Notation":"A1:L31"}
2025-08-01 23:09:15 - INFO - ✅ 成功写入数据到工作表 st-8072ca1b-33873!A1:L31
2025-08-01 23:09:15 - INFO - ✅ 数据写入成功
2025-08-01 23:09:15 - INFO - 🎨 应用格式设置...
2025-08-01 23:09:15 - INFO - 📐 设置所有数据居中对齐...
2025-08-01 23:09:15 - INFO - 🔍 格式设置调试:
2025-08-01 23:09:15 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq/sheets/st-8072ca1b-33873/ranges/A1:L31?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-08-01 23:09:15 - INFO - 格式数据: {'horizontalAlignments': [['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center']], 'verticalAlignments': [['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle']]}
2025-08-01 23:09:16 - INFO - 响应状态码: 200
2025-08-01 23:09:16 - INFO - 响应内容: {"a1Notation":"A1:L31"}
2025-08-01 23:09:16 - INFO - 🔗 合并标题行: A1:L1
2025-08-01 23:09:16 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:16 - INFO - ✅ 成功合并单元格: A1:L1
2025-08-01 23:09:16 - ERROR - urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'api.dingtalk.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
2025-08-01 23:09:16 - INFO - ✅ 成功设置合并单元格的值和格式: A1 = '2025年07月数据汇总'
2025-08-01 23:09:16 - INFO - ✅ 成功合并标题: 2025年07月数据汇总
2025-08-01 23:09:16 - INFO - 🎉 月度汇总数据已成功写入Sheet2！
2025-08-01 23:09:16 - INFO - 🎉 报告流程处理完成.
