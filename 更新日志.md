# 项目更新日志

## 2025-01-27 21:00
### 新增时间控制功能
- 新增21点时间控制逻辑：
  - 21点前运行：待补货、待处理、已审核、发货订单数显示"暂无数据（21点后显示）"
  - 21点后运行：显示所有完整数据
  - 待领料、已领料数据不受时间限制，始终显示
- 新增测试命令：
  - `python warehouse_report.py test-before` - 模拟21点前运行效果
  - `python warehouse_report.py test-after` - 模拟21点后运行效果
- 时间判断基于系统当前小时（datetime.now().hour >= 21）
- 更新README文档：说明时间控制功能和测试命令
- 完善用户体验：程序运行时显示当前时间和21点后数据显示状态
- 测试验证：21点前只显示待领料、已领料，其他部分提示21点后显示

## 2025-01-27 20:30
### 新增Excel发货数据处理功能
- 创建 `excel_processor.py` - Excel数据处理模块
- 新增功能：
  - 自动扫描项目目录中包含"发货数据"关键词的Excel文件
  - 智能识别Excel中的订单编号、应收金额、预估总重量列
  - 支持多种列名变体自动匹配（如"订单编号"/"订单号"/"单号"）
  - 汇总所有Excel文件的订单数据：订单数量、发货总金额、发货总重量
  - 容错处理：数值列自动转换，忽略无效数据
- 更新主程序 `warehouse_report.py`：
  - 集成Excel处理器，程序运行时自动扫描Excel文件
  - 在钉钉报告中新增"发货订单数"部分
  - 显示格式：`- **订单数：** XXX`、`- **发货总金额：** XXX`、`- **发货总重量：** XXX`
- 更新依赖包：添加 `pandas` 和 `openpyxl` 用于Excel处理
- 更新打包配置：包含新的Excel处理模块和依赖
- 更新README文档：说明Excel文件要求和新功能使用方法
- 支持.xlsx和.xls格式的Excel文件
- 数据为空时显示"暂无发货数据"，保持简洁体验

## 2025-01-27 19:45
### 新增exe打包功能
- 创建 `build_exe.py` - 自动化打包脚本，使用PyInstaller
- 新增 `build.bat` - 一键打包批处理文件
- 创建 `requirements_build.txt` - 打包依赖配置
- 新增 `打包说明.md` - 详细的打包使用指南
- 打包特性：
  - 将代码和配置文件内置到exe中
  - 保留 `仓库数据.json` 作为外部文件，方便用户修改
  - 自动创建发布包，包含使用说明
  - 支持清理构建文件功能
- 分发友好：用户只需exe文件和数据文件即可使用

## 2025-01-27 19:30
### 项目代码清理优化
- 删除所有图片相关的代码和文件（table_image_generator.py）
- 清理warehouse_report.py中的图片生成功能
- 移除钉钉工具类中的图片上传和发送方法
- 简化dependencies，只保留requests库
- 更新README文档，移除图片相关说明
- 删除Python缓存文件(__pycache__)
- 简化程序运行参数，专注于文本格式报告
- 项目结构更加简洁，只保留核心功能

## 2025-01-27 19:15
### 添加数据判断逻辑
- 新增智能数据判断功能
- 当JSON数据项为空或0时，显示"暂无未完成单据"
- 提升用户体验，避免显示冷冰冰的数字0
- 保持简洁的列表格式不变

## 2025-01-27 19:10
### 格式重新设计为简洁样式
- 完全重新设计报告格式，采用简洁的列表式布局
- 标题改为二级标题：📦 仓库发货数据
- 数据生成时间移至顶部显示
- 移除所有表格，改用简洁的项目符号列表
- 每个状态模块用分隔线(---)分开，视觉层次清晰
- 只保留核心数据统计，移除详细订单列表
- 整体格式更加现代化和易读

## 2025-01-27 17:30
### 图片报告功能实现
- 新增表格图片生成功能，解决钉钉中表格格式显示问题
- 创建 `table_image_generator.py` - 专门的表格图片生成工具类
- 更新钉钉工具类，支持图片上传和发送功能
- 程序现在默认发送图片格式报告，表格显示更美观
- 新增多种运行模式：
  - `python warehouse_report.py` - 发送图片报告（默认）
  - `python warehouse_report.py image` - 显式指定图片模式
  - `python warehouse_report.py markdown` - 发送文本格式报告
  - `python warehouse_report.py preview` - 预览图片但不发送
  - `python warehouse_report.py test` - 测试钉钉连接
- 更新依赖包：添加 matplotlib、pandas、Pillow、numpy
- 图片自动清理：发送完成后删除临时图片文件
- 容错机制：图片生成失败时自动回退到文本模式

## 2025-01-27 15:45
### 钉钉通知程序开发完成
- 创建了完整的仓库数据钉钉通知Python程序
- 主要文件：
  - `warehouse_report.py` - 主程序文件
  - `dingtalk_utils.py` - 钉钉工具类，支持签名验证
  - `config.json` - 配置文件
  - `requirements.txt` - 依赖包列表
  - `README.md` - 详细使用说明
- 程序功能：
  - 读取 `仓库数据.json` 并生成横向表格格式的Markdown报告
  - 支持钉钉机器人消息发送（Markdown和文本格式）
  - 包含预览模式、测试模式和正常发送模式
  - 完善的错误处理和日志输出
- 表格格式完全按照用户要求的横向布局设计
- 支持钉钉签名验证，提高安全性

## 2025-01-27 14:30
### 仓库数据文件创建
- 创建了 `仓库数据.json` 文件
- 整理并结构化了仓库发货数据，包括：
  - 待补货：品数121，订单数100
  - 待处理：品数121，订单数100  
  - 待领料：7个待审核订单（PS2025062306-PS2025062312）
  - 已领料：6个已完成订单（PS2025062111, PS2025062112, PS2025062205, PS2025062206, PS2025062302, PS2025062303）
  - 已审核：订单数100
- 添加了统计汇总信息便于数据分析
- JSON格式便于系统集成和数据处理 