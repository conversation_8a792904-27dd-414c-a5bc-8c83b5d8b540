#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仓库发货数据钉钉通知程序
"""

import json
import os
import sys
import time
import requests
from datetime import datetime
from typing import Dict, List
from dingtalk_utils import DingTalkUtils
from dingtalk_sheet_utils import DingTalkSheetUtils
from excel_processor import ExcelProcessor
from logging_utils import setup_logging
from monthly_summary_generator import run_auto_monthly_summary


def load_config(config_file="config.json"):
    """
    加载配置文件
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        配置字典
    """
    try:
        if not os.path.exists(config_file):
            # 在日志设置前，这个print是必须的
            print(f"❌ 配置文件 {config_file} 不存在")
            sys.exit(1)
            
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        print(f"✅ 配置文件加载成功: {config_file}")
        return config
        
    except Exception as e:
        print(f"❌ 加载配置文件失败: {str(e)}")
        sys.exit(1)


class WarehouseReportSender:
    PENDING_ORDER_CHUNK_SIZE = 150  # 每条钉钉消息包含的订单号数量

    def __init__(self, config):
        """
        初始化仓库报告发送器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.config_file = "config.json"  # 保存时需要
        
        # 初始化钉钉工具
        if self.config.get('dingtalk', {}).get('enabled', False):
            webhook_url = self.config['dingtalk']['webhook_url']
            secret = self.config['dingtalk'].get('secret')
            agent_id = self.config['dingtalk'].get('agent_id')

            # 获取access_token
            access_token = None
            sheet_config = self.config.get('dingtalk', {}).get('sheet', {})
            if sheet_config.get('app_key') and sheet_config.get('app_secret'):
                print("🔑 正在获取钉钉access_token...")
                success, token_or_error = DingTalkUtils.get_access_token(
                    sheet_config['app_key'],
                    sheet_config['app_secret']
                )
                if success:
                    access_token = token_or_error
                else:
                    print(f"⚠️ 获取access_token失败: {token_or_error}")

            self.dingtalk = DingTalkUtils(webhook_url, secret, access_token, agent_id)
        else:
            self.dingtalk = None
            print("⚠️ 钉钉通知已禁用")

        # 初始化钉钉表格工具
        sheet_config = self.config.get('dingtalk', {}).get('sheet', {})
        if sheet_config.get('enabled', False):
            try:
                self.dingtalk_sheet = DingTalkSheetUtils(self.config)
                self.sheet_name = sheet_config.get('sheet_name', 'Sheet1')
            except Exception as e:
                print(f"⚠️ 钉钉表格工具初始化失败: {str(e)}")
                self.dingtalk_sheet = None
        else:
            self.dingtalk_sheet = None
            print("⚠️ 钉钉表格功能已禁用")

        # 初始化Excel处理器
        self.excel_processor = ExcelProcessor(self.config)
        

    
    def save_config(self):
        """
        保存配置文件
        """
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            print(f"✅ 配置文件保存成功: {self.config_file}")
            return True
        except Exception as e:
            print(f"⚠️ 保存配置文件失败: {str(e)}")
            return False

    def generate_main_report(self, status_data, shipping_data, is_after_threshold, pending_list_exists):
        """
        生成主报告的Markdown内容 (不包含待处理订单列表)
        
        Returns:
            Markdown格式的报告字符串
        """
        try:
            # 获取各项数据
            waiting_stock = status_data.get('待补货', {})
            waiting_process = status_data.get('待处理', {})
            unfinished_production = status_data.get('生产单未完成单数', {})
            total_production = status_data.get('总生产单数', {})
            audited_custom = status_data.get('已审核-定制', {})
            audited_stock = status_data.get('已审核-现货', {})
            
            # 辅助函数：判断数据是否为空并返回相应显示
            def format_data(value, unit=""):
                if value is None or value == 0 or value == "":
                    return "暂无未完成单据"
                return f"{value}{unit}"
            
            # 待补货状态处理
            if not is_after_threshold:
                waiting_stock_content = "- 暂无数据"
            else:
                waiting_stock_items = waiting_stock.get('品数', 0)
                waiting_stock_orders = waiting_stock.get('订单数', 0)
                if waiting_stock_items == 0 and waiting_stock_orders == 0:
                    waiting_stock_content = "- 暂无未完成单据"
                else:
                    waiting_stock_content = f"""- **品数：** {format_data(waiting_stock_items)}
- **订单数：** {format_data(waiting_stock_orders)}"""
            
            # 待处理状态处理
            if not is_after_threshold:
                waiting_process_content = "- 暂无数据"
            else:
                waiting_process_items = waiting_process.get('品数', 0)
                waiting_process_orders = waiting_process.get('订单数', 0)
                if waiting_process_items == 0 and waiting_process_orders == 0:
                    waiting_process_content = "- 暂无未完成单据"
                else:
                    waiting_process_content = f"""- **品数：** {format_data(waiting_process_items)}
- **订单数：** {format_data(waiting_process_orders)}"""
            
            # 生产单完成进度处理
            unfinished_count = unfinished_production.get('总数', 0)
            total_count = total_production.get('总数', 0)

            if total_count == 0:
                production_progress_content = "- 暂无生产单数据"
            else:
                finished_count = total_count - unfinished_count
                production_progress_content = f"- **未完成：** {unfinished_count}/{total_count}，**已完成：** {finished_count}/{total_count}"
            
            # 已审核-定制状态处理
            if not is_after_threshold:
                audited_custom_content = "- 暂无数据"
            else:
                audited_custom_count = audited_custom.get('订单数', 0)
                audited_custom_content = f"- **订单数：** {format_data(audited_custom_count)}"

            # 已审核-现货状态处理
            if not is_after_threshold:
                audited_stock_content = "- 暂无数据"
            else:
                audited_stock_count = audited_stock.get('订单数', 0)
                audited_stock_content = f"- **订单数：** {format_data(audited_stock_count)}"
            
            # 发货订单数据处理
            if not is_after_threshold:
                shipping_content = "- 暂无数据"
            else:
                stock_order_count = shipping_data.get('现货订单数', 0)
                custom_order_count = shipping_data.get('定制订单数', 0)
                stock_weight = shipping_data.get('现货订单发货总重量', 0.0)
                custom_weight = shipping_data.get('定制订单发货总重量', 0.0)

                if stock_order_count == 0 and custom_order_count == 0 and stock_weight == 0 and custom_weight == 0:
                    shipping_content = "- 暂无发货数据"
                else:
                    shipping_content = f"""- **现货订单数：** {format_data(stock_order_count)}
- **现货订单发货总重量：** {format_data(stock_weight, 'kg')}
- **定制订单数：** {format_data(custom_order_count)}
- **定制订单发货总重量：** {format_data(custom_weight, 'kg')}"""
            
            # 如果有待处理订单，添加提示信息
            pending_orders_hint = ""
            if pending_list_exists and is_after_threshold:
                pending_orders_hint = "\n\n---\n\n*稍后将分条发送待处理订单列表...*"

            markdown = f"""## 📦 仓库发货数据

---
*数据生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M')}*

---

### 待补货
{waiting_stock_content}

---

### 待处理
{waiting_process_content}

---

### 生产单完成进度
{production_progress_content}

---

### 已审核-定制
{audited_custom_content}

---

### 已审核-现货
{audited_stock_content}

---

### 发货订单数
{shipping_content}{pending_orders_hint}"""
            
            return markdown
            
        except Exception as e:
            print(f"❌ 生成主报告失败: {str(e)}")
            return None

    def calculate_reward_penalty(self, status_data: Dict) -> Dict:
        """
        计算奖惩金额

        Args:
            status_data: 仓库状态数据

        Returns:
            包含奖惩计算结果的字典
        """
        try:
            # 提取基础数据
            audited_custom_data = status_data.get('已审核-定制', {})
            audited_stock_data = status_data.get('已审核-现货', {})
            waiting_stock_data = status_data.get('待补货', {})
            waiting_process_data = status_data.get('待处理', {})

            # 计算已审核订单总数（定制+现货）
            audited_custom_orders = audited_custom_data.get('订单数', 0)
            audited_stock_orders = audited_stock_data.get('订单数', 0)
            audited_orders = audited_custom_orders + audited_stock_orders

            waiting_stock_orders = waiting_stock_data.get('订单数', 0)
            waiting_process_orders = waiting_process_data.get('订单数', 0)

            print(f"📊 奖惩计算基础数据：")
            print(f"   已审核-定制：{audited_custom_orders}单")
            print(f"   已审核-现货：{audited_stock_orders}单")
            print(f"   已审核订单总计：{audited_orders}单 (定制+现货)")
            print(f"   待补货订单：{waiting_stock_orders}单")
            print(f"   待处理订单：{waiting_process_orders}单")

            # 第一层奖惩：基于待补货订单（10元）
            if audited_orders <= 4000:
                stock_threshold = 15
                stock_condition = f"已审核≤4000且待补货≤{stock_threshold}"
            else:
                stock_threshold = 25
                stock_condition = f"已审核>4000且待补货≤{stock_threshold}"

            if waiting_stock_orders <= stock_threshold:
                stock_reward = 10
                stock_result = "奖励"
            else:
                stock_reward = -10
                stock_result = "惩罚"

            # 第二层奖惩：基于待处理订单（25元）
            if audited_orders <= 4000:
                process_threshold = int(audited_orders * 0.01)  # 1%
                process_condition = f"已审核≤4000且待处理≤{process_threshold}（1%）"
            else:
                process_threshold = int(audited_orders * 0.015)  # 1.5%
                process_condition = f"已审核>4000且待处理≤{process_threshold}（1.5%）"

            if waiting_process_orders <= process_threshold:
                process_reward = 25
                process_result = "奖励"
            else:
                process_reward = -25
                process_result = "惩罚"

            # 计算总奖惩
            total_reward = stock_reward + process_reward

            return {
                'audited_orders': audited_orders,
                'waiting_stock_orders': waiting_stock_orders,
                'waiting_process_orders': waiting_process_orders,
                'stock_threshold': stock_threshold,
                'stock_condition': stock_condition,
                'stock_reward': stock_reward,
                'stock_result': stock_result,
                'process_threshold': process_threshold,
                'process_condition': process_condition,
                'process_reward': process_reward,
                'process_result': process_result,
                'total_reward': total_reward
            }

        except Exception as e:
            print(f"❌ 奖惩计算失败: {str(e)}")
            return None

    def generate_reward_penalty_report(self, reward_data: Dict) -> str:
        """
        生成奖惩报告的Markdown文本

        Args:
            reward_data: 奖惩计算结果

        Returns:
            Markdown格式的奖惩报告
        """
        if not reward_data:
            return ""

        # 格式化数字显示
        audited = f"{reward_data['audited_orders']:,}"
        waiting_stock = f"{reward_data['waiting_stock_orders']:,}"
        waiting_process = f"{reward_data['waiting_process_orders']:,}"

        report = f"""奖惩计算结果

基础数据：

已审核订单：{audited}单

待补货订单：{waiting_stock}单

待处理订单：{waiting_process}单

待补货奖惩（10元规则）：
条件：{reward_data['stock_condition']}
结果：{reward_data['stock_result']} {reward_data['stock_reward']:+d}元

待处理奖惩（25元规则）：
条件：{reward_data['process_condition']}
结果：{reward_data['process_result']} {reward_data['process_reward']:+d}元

总计奖惩：{reward_data['total_reward']:+d}元"""

        return report

    def send_personal_reward_report(self, reward_report: str) -> bool:
        """
        发送奖惩报告给指定个人

        Args:
            reward_report: 奖惩报告内容

        Returns:
            是否发送成功
        """
        try:
            # 从配置文件中获取当前环境和用户信息
            current_env = self.config.get('dingtalk', {}).get('current_environment', 'prod')
            users_config = self.config.get('dingtalk', {}).get('users', {})

            if current_env == 'test':
                # 测试环境：发送给杜陶祎（信息部）
                user_info = users_config.get('test_user', {})
                target_userid = user_info.get('user_id', "01415968542326653172")
                user_name = user_info.get('name', "杜陶祎（信息部）")
                env_desc = "测试环境"
            else:
                # 正式环境：发送给宋继文（义乌仓储部）
                user_info = users_config.get('production_user', {})
                target_userid = user_info.get('user_id', "1529202823554123")
                user_name = user_info.get('name', "宋继文（义乌仓储部）")
                env_desc = "正式环境"

            print(f"📤 当前环境: {env_desc}")
            print(f"📤 发送目标: {user_name} (userId: {target_userid})")

            # 直接调用钉钉工作通知发送方法
            success = self.send_work_notice_to_user(target_userid, "奖惩计算结果", reward_report)

            if success:
                print(f"✅ 奖惩报告已发送给{user_name} - {env_desc}")
                return True
            else:
                print(f"❌ 发送奖惩报告失败")
                return False

        except Exception as e:
            print(f"❌ 发送个人奖惩报告时出错: {str(e)}")
            return False

    def send_work_notice_to_user(self, userid: str, title: str, content: str) -> bool:
        """
        使用机器人发送个人消息给指定用户

        Args:
            userid: 用户ID
            title: 消息标题
            content: 消息内容

        Returns:
            是否发送成功
        """
        try:
            print(f"📤 使用机器人发送个人消息给用户 {userid}")
            print(f"📋 消息标题: {title}")
            print(f"📄 消息内容: {content[:100]}...")

            # 获取配置信息
            sheet_config = self.config.get('dingtalk', {}).get('sheet', {})
            app_key = sheet_config.get('app_key')
            app_secret = sheet_config.get('app_secret')
            robot_code = app_key  # 机器人代码就是app_key

            if not app_key or not app_secret:
                print("❌ 缺少app_key或app_secret配置")
                return False

            # 获取access_token
            token_url = "https://api.dingtalk.com/v1.0/oauth2/accessToken"
            token_data = {
                "appKey": app_key,
                "appSecret": app_secret
            }
            token_headers = {"Content-Type": "application/json"}

            token_response = requests.post(token_url, json=token_data, headers=token_headers, verify=False, timeout=30)
            if token_response.status_code != 200:
                print(f"❌ 获取access_token失败: {token_response.text}")
                return False

            access_token = token_response.json().get("accessToken")
            if not access_token:
                print(f"❌ 获取access_token失败: {token_response.json()}")
                return False

            # 发送机器人单聊消息
            oto_url = "https://oapi.dingtalk.com/v1.0/robot/oToMessages/batchSend"
            oto_headers = {
                'Content-Type': "application/json",
                'Host': 'api.dingtalk.com',
                'x-acs-dingtalk-access-token': access_token
            }

            # 构造消息参数
            msg_param = {
                "title": title,
                "text": content
            }

            oto_data = {
                "robotCode": robot_code,
                "userIds": [userid],
                "msgKey": "sampleMarkdown",
                "msgParam": json.dumps(msg_param, ensure_ascii=False)
            }

            oto_response = requests.post(oto_url, headers=oto_headers, json=oto_data, timeout=10, verify=False)
            result = oto_response.json()

            print(f"📊 发送结果: {result}")

            if result.get('errcode') == 0 or 'processQueryKey' in result:
                print(f"✅ 机器人消息发送成功")
                if 'processQueryKey' in result:
                    print(f"   任务查询键: {result['processQueryKey']}")
                return True
            else:
                print(f"❌ 机器人消息发送失败: {result}")
                return False

        except Exception as e:
            print(f"❌ 发送机器人消息时出错: {str(e)}")
            return False

    def send_pending_orders_list(self, pending_orders: List[str]) -> bool:
        """
        分条发送待处理订单列表到钉钉群

        Args:
            pending_orders: 待处理订单号列表

        Returns:
            是否发送成功
        """
        if not pending_orders:
            print("📋 没有待处理订单需要发送")
            return True

        try:
            total_orders = len(pending_orders)
            chunk_size = self.PENDING_ORDER_CHUNK_SIZE
            total_chunks = (total_orders + chunk_size - 1) // chunk_size  # 向上取整

            print(f"📋 准备分条发送 {total_orders} 个待处理订单，分为 {total_chunks} 条消息")

            for i in range(0, total_orders, chunk_size):
                chunk = pending_orders[i:i + chunk_size]
                chunk_num = i // chunk_size + 1

                # 构建订单列表消息 - 使用Markdown无序列表格式
                orders_lines = []
                for j in range(0, len(chunk), 2):  # 每行显示2个订单号
                    if j + 1 < len(chunk):
                        orders_lines.append(f"- {chunk[j]}   {chunk[j+1]}")
                    else:
                        orders_lines.append(f"- {chunk[j]}")

                orders_text = "\n\n".join(orders_lines)
                message_title = f"📋 待处理订单列表 ({chunk_num}/{total_chunks})"

                # 根据是否为最后一批决定结尾格式
                if chunk_num == total_chunks:
                    # 最后一批显示完整的总计信息
                    message_content = f"""{orders_text}

总计: 共 {total_orders} 条待处理订单"""
                else:
                    # 非最后一批显示当前批次信息
                    message_content = f"""{orders_text}

---
*第 {chunk_num}/{total_chunks} 批，共 {total_orders} 条待处理订单*"""

                # 发送消息
                if self.dingtalk:
                    success, result = self.dingtalk.send_markdown_message(message_title, message_content)
                    if success:
                        print(f"✅ 第 {chunk_num}/{total_chunks} 批待处理订单发送成功 ({len(chunk)} 个订单)")
                    else:
                        print(f"❌ 第 {chunk_num}/{total_chunks} 批待处理订单发送失败: {result}")
                        return False

                    # 避免发送过快，添加延迟
                    if chunk_num < total_chunks:
                        time.sleep(1)

            print(f"🎉 所有待处理订单列表发送完成！共发送 {total_chunks} 条消息，{total_orders} 个订单")
            return True

        except Exception as e:
            print(f"❌ 发送待处理订单列表时出错: {str(e)}")
            return False

    def append_reward_to_sheet(self, reward_data: Dict) -> bool:
        """
        将奖惩结果追加到钉钉表格的最后一行

        Args:
            reward_data: 奖惩数据字典

        Returns:
            是否追加成功
        """
        try:
            # 获取第一个工作表的ID
            sheets = self.dingtalk_sheet.get_sheets_list()
            if not sheets:
                print("❌ 无法获取工作表列表")
                return False

            sheet_id = sheets[0].get('id', 'Sheet1')

            # 获取配置文件中记录的最后一行
            last_row_hint = self.dingtalk_sheet.last_row if self.dingtalk_sheet.last_row > 1 else None

            if last_row_hint:
                # 使用配置文件中的最后一行作为起始点
                next_row = last_row_hint + 1
                print(f"📍 使用配置文件中记录的最后一行: {last_row_hint}，奖惩结果将写入第 {next_row} 行")
            else:
                # 如果没有记录，查找实际的最后一行
                last_row = self.dingtalk_sheet.find_last_data_row(sheet_id)
                next_row = last_row + 1
                print(f"📍 查找到最后数据行: {last_row}，奖惩结果将写入第 {next_row} 行")

            # 构造奖惩结果详细数据（多行）
            reward_detail_data = [
                ["奖惩计算结果", "", ""],
                [f"已审核订单：{reward_data['audited_orders']:,}单  待补货订单：{reward_data['waiting_stock_orders']}单", "", ""],
                [f"待处理订单：{reward_data['waiting_process_orders']}单", "", ""],
                [f"待补货奖惩（10元规则）：{reward_data['stock_condition']}", "", ""],
                [f"结果：{reward_data['stock_result']} {abs(reward_data['stock_reward'])}元", "", ""],
                [f"待处理奖惩（25元规则）：{reward_data['process_condition']}", "", ""],
                [f"结果：{reward_data['process_result']} {abs(reward_data['process_reward'])}元", "", ""],
                [f"总计奖惩：{reward_data['total_reward']:+d}元", "", ""]
            ]

            # 计算写入范围（多行）
            end_row = next_row + len(reward_detail_data) - 1
            range_address = f"A{next_row}:C{end_row}"

            print(f"📝 正在写入奖惩结果到 {range_address}")

            # 写入数据
            success = self.dingtalk_sheet.write_cell_range(
                sheet_id=sheet_id,
                cell_range=range_address,
                values=reward_detail_data
            )

            if success:
                # 为每一行合并单元格
                merge_success_count = 0
                for i, row_data in enumerate(reward_detail_data):
                    row_num = next_row + i
                    row_range = f"A{row_num}:C{row_num}"
                    merge_success = self.dingtalk_sheet._merge_cells(
                        sheet_id=sheet_id,
                        cell_range=row_range,
                        value=row_data[0]
                    )
                    if merge_success:
                        merge_success_count += 1

                if merge_success_count > 0:
                    print(f"✅ 奖惩详细结果成功追加到第 {next_row}-{end_row} 行")

                    # 更新配置文件中的最后一行记录
                    self.dingtalk_sheet.update_last_row(end_row)

                    return True
                else:
                    print("⚠️ 奖惩结果写入成功，但格式设置失败")
                    return True
            else:
                print("❌ 奖惩结果写入失败")
                return False

        except Exception as e:
            print(f"❌ 追加奖惩结果时出错: {str(e)}")
            return False

    def send_report(self):
        """
        发送仓库报告, 支持更新或追加当天数据
        """
        print("🚀 开始生成并发送仓库报告...")

        self._process_and_write_data()

        print("\n--- 阶段三：开始生成月度汇总 ---")
        try:
            run_auto_monthly_summary()
        except Exception as e:
            print(f"❌ 生成月度汇总时发生错误: {str(e)}")

        print("\n🎉 报告流程处理完成.")
        return True

    def _process_and_write_data_with_full_flow(self, target_date: datetime) -> bool:
        """
        强制执行完整流程的数据处理方法（用于重新执行历史数据）
        无论当前时间如何，都按23点后的完整流程执行

        Args:
            target_date: 指定的目标日期时间

        Returns:
            是否成功
        """
        date_str = target_date.strftime('%Y-%m-%d')
        print(f"\n🏭 开始处理日期为 {date_str} 的数据（强制完整流程）...")

        # 1. 处理Excel数据
        status_data = self.excel_processor.process_warehouse_status_data(target_date)
        if not status_data:
            print(f"❌ 未能从Excel中获取日期为 {date_str} 的仓库状态数据，跳过处理。")
            return False

        shipping_data = status_data
        pending_order_list = self.excel_processor.process_pending_orders_file()

        # 2. 强制设置为达到阈值（模拟23点后的完整流程）
        is_after_threshold = True
        print(f"🕚 强制按23点后完整流程执行：群通知 + 个人通知 + 表格写入")

        # 3. 发送主报告到群
        title = self.config.get('report_title', '📦 仓库发货数据报告')
        main_report_md = self.generate_main_report(status_data, shipping_data, is_after_threshold, bool(pending_order_list))
        if not main_report_md:
            print("❌ 生成主报告失败")
            return False

        if self.dingtalk:
            print("📤 正在发送主报告到群...")
            success, result = self.dingtalk.send_markdown_message(title, main_report_md)
            if not success:
                print(f"❌ 主报告发送失败: {result}")
            else:
                print("✅ 主报告发送成功！")

            # 发送待处理订单列表（如果存在且达到阈值）
            if pending_order_list and is_after_threshold:
                print("📤 正在分条发送待处理订单列表...")
                self.send_pending_orders_list(pending_order_list)

        # 4. 计算和发送奖惩数据到个人
        reward_data = self.calculate_reward_penalty(status_data)
        if reward_data:
            reward_report = self.generate_reward_penalty_report(reward_data)
            if reward_report:
                time.sleep(1)
                print(f"📤 正在发送奖惩报告到个人...")
                self.send_personal_reward_report(reward_report)

        # 5. 写入钉钉表格
        if self.dingtalk_sheet:
            print("📊 正在将报告块写入到钉钉表格...")
            try:
                sheets = self.dingtalk_sheet.get_sheets_list()
                if sheets:
                    sheet_id = sheets[0].get('id', 'Sheet1')
                    report_date_str = status_data.get('report_date', target_date.strftime('%Y/%m/%d'))

                    # 对于历史数据重新执行，总是追加新的数据块
                    print(f"📝 追加日期为 {report_date_str} 的历史数据到表格...")

                    success, end_row = self.dingtalk_sheet.append_warehouse_report_block(
                        sheet_id,
                        status_data,
                        shipping_data,
                        reward_data=reward_data,  # 添加奖惩数据
                        start_row_override=None  # 总是追加
                    )

                    if success:
                        print("✅ 历史数据成功写入钉钉表格！")
                        if end_row and end_row > self.dingtalk_sheet.last_row:
                            self.dingtalk_sheet.update_last_row(end_row)
                    else:
                        print("❌ 历史数据写入钉钉表格失败")
                else:
                    print("❌ 无法获取钉钉表格工作表列表")
            except Exception as e:
                print(f"❌ 钉钉表格操作失败: {str(e)}")
        else:
            print("⚠️ 钉钉表格功能未启用，跳过表格写入")

        # 6. 生成月度汇总（Sheet2）
        print(f"📊 开始生成月度汇总数据...")
        try:
            run_auto_monthly_summary()
            print(f"✅ 月度汇总数据生成完成")
        except Exception as e:
            print(f"❌ 生成月度汇总时发生错误: {str(e)}")

        print(f"🎉 日期 {date_str} 的完整流程处理完成！")
        return True

    def _process_and_write_data(self, target_date: datetime = None) -> bool:
        """
        核心处理流程：获取数据、生成报告、写入表格
        
        Args:
            target_date: 如果提供，则处理指定日期的数据；否则处理最新的数据
        """
        # 1. 首先处理所有Excel数据
        if target_date:
            date_str = target_date.strftime('%Y-%m-%d')
            print(f"\n🏭 开始处理日期为 {date_str} 的数据...")
            status_data = self.excel_processor.process_warehouse_status_data(target_date)
        else:
            print("\n🏭 开始处理最新的数据...")
            status_data = self.excel_processor.process_warehouse_status_data()

        # shipping_data 和 pending_order_list 不再需要从单独的文件读取
        # shipping_data 现在由 process_warehouse_status_data一并提供
        shipping_data = status_data 
        pending_order_list = self.excel_processor.process_pending_orders_file()

        if not status_data:
            if target_date:
                print(f"❌ 未能从Excel中获取日期为 {date_str} 的仓库状态数据，跳过处理。")
            else:
                print("❌ 未能从Excel中获取最新的仓库状态数据，中止报告生成。")
            return False
        
        # 2. 判断时间阈值（仅对最新数据的主报告和奖惩发送有效）
        is_after_threshold = True
        current_hour = datetime.now().hour
        if not target_date: # 只有在处理最新数据时才检查时间
            report_hour_threshold = self.config.get('report_time_threshold_hour', 21)
            is_after_threshold = current_hour >= report_hour_threshold
            print(f"数据写入阈值: {report_hour_threshold}点, 当前是否达到: {'是' if is_after_threshold else '否'}")
        
        # 3. 发送主报告 (仅当处理最新数据时)
        if not target_date:
            title = self.config.get('report_title', '📦 仓库发货数据报告')
            main_report_md = self.generate_main_report(status_data, shipping_data, is_after_threshold, bool(pending_order_list))
            if not main_report_md:
                return False
            if self.dingtalk:
                print("📤 正在发送主报告...")
                success, result = self.dingtalk.send_markdown_message(title, main_report_md)
                if not success:
                    print(f"❌ 主报告发送失败: {result}")
                else:
                    print("✅ 主报告发送成功！")

                # 发送待处理订单列表（如果存在且达到阈值）
                if pending_order_list and is_after_threshold:
                    print("📤 正在分条发送待处理订单列表...")
                    self.send_pending_orders_list(pending_order_list)

        # 4. 计算和发送奖惩数据（仅当处理最新数据且达到阈值时）
        reward_data = self.calculate_reward_penalty(status_data)
        if not target_date and reward_data and is_after_threshold:
            reward_report = self.generate_reward_penalty_report(reward_data)
            if reward_report:
                time.sleep(1)
                print(f"📤 正在发送奖惩报告...")
                self.send_personal_reward_report(reward_report)
        
        # 5. 写入钉钉表格
        if self.dingtalk_sheet and is_after_threshold:
            print("📊 正在将报告块写入到钉钉表格...")
            try:
                sheets = self.dingtalk_sheet.get_sheets_list()
                if sheets:
                    sheet_id = sheets[0].get('id', 'Sheet1')
                    report_date_str = status_data.get('report_date', datetime.now().strftime('%Y/%m/%d'))
                    
                    # 只有在处理最新数据时才检查更新，补全数据总是追加
                    start_row_for_write = None
                    if not target_date:
                        print(f"📝 检查日期为 {report_date_str} 的数据是否已存在...")
                        start_row_for_write = self.dingtalk_sheet.find_report_block_by_date(sheet_id, report_date_str)
                        if start_row_for_write:
                            print(f"🔄 检测到今天的数据已存在于第 {start_row_for_write} 行，将执行覆盖更新...")
                    
                    # 如果不是更新操作，则获取追加行号
                    if not start_row_for_write:
                        last_row_hint = self.dingtalk_sheet.last_row if self.dingtalk_sheet.last_row > 1 else None
                        start_row_for_write = self.dingtalk_sheet._get_next_available_row(sheet_id, last_row_hint)

                    success, end_row = self.dingtalk_sheet.append_warehouse_report_block(
                        sheet_id=sheet_id,
                        status_data=status_data,
                        shipping_data=shipping_data,
                        reward_data=reward_data,
                        start_row_override=start_row_for_write
                    )

                    if success:
                        print("✅ 数据成功写入钉钉表格！")
                        if end_row and (not target_date or end_row > self.dingtalk_sheet.last_row):
                            self.dingtalk_sheet.update_last_row(end_row)
                    else:
                        print("❌ 数据写入钉钉表格失败")

                else:
                    print("❌ 无法获取工作表列表")
            except Exception as e:
                print(f"❌ 写入钉钉表格时出现错误: {str(e)}")
        
        return True

    def send_report_old(self):
        """
        旧的发送报告方法，保留作为参考
        """
        pass # 旧代码已移除，此方法作为占位符

    def test_connection(self):
        """
        测试钉钉连接
        
        Returns:
            测试结果
        """
        if not self.dingtalk:
            print("⚠️ 钉钉通知已禁用，无法测试连接")
            return False
        
        print("🔍 正在测试钉钉连接...")
        test_message = "🧪 这是一条测试消息，用于验证钉钉机器人连接是否正常。"
        
        success, result = self.dingtalk.send_text_message(test_message)
        
        if success:
            print("✅ 钉钉连接测试成功！")
            return True
        else:
            print(f"❌ 钉钉连接测试失败: {result}")
            return False


def main():
    """
    主函数
    """
    config = load_config()
    setup_logging(config)
    
    print("🏭 仓库发货数据钉钉通知程序")
    print("=" * 40)
    
    # --- 命令行参数处理 ---
    # 模式一：测试钉钉连接
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        sender = WarehouseReportSender(config)
        sender.test_connection()
        return
        
    # 模式二：重新执行指定日期的完整流程（新增功能）
    if len(sys.argv) > 1 and not "T" in sys.argv[1] and sys.argv[1] != "test":
        try:
            # 解析日期字符串（支持 YYYY-MM-DD 格式）
            date_str = sys.argv[1]
            target_date = datetime.strptime(date_str, '%Y-%m-%d')
            # 设置为23点，模拟晚上的完整流程
            target_date = target_date.replace(hour=23, minute=0, second=0)

            print(f"🔄 重新执行模式：处理日期 {date_str} 的完整流程")
            print(f"📅 目标时间设定为: {target_date.strftime('%Y-%m-%d %H:%M:%S')}")
            print("⚠️  将按23点后的完整流程执行（群通知+个人通知+表格写入）")

            sender = WarehouseReportSender(config)

            # 使用指定日期处理数据，但强制执行完整流程
            success = sender._process_and_write_data_with_full_flow(target_date)

            if success:
                print(f"✅ 日期 {date_str} 的完整流程重新执行成功！")
            else:
                print(f"❌ 日期 {date_str} 的流程执行失败，请检查数据是否存在。")

            return

        except ValueError as e:
            print(f"❌ 日期格式错误: {e}")
            print("💡 请使用格式: YYYY-MM-DD，例如: 2025-07-17")
            return
        except Exception as e:
            print(f"❌ 重新执行模式失败: {str(e)}")
            return

    # 模式三：模拟指定时间的报告生成（保持原有功能）
    if len(sys.argv) > 1 and "T" in sys.argv[1]:
        try:
            # 解析ISO格式的日期时间字符串
            sim_time_str = sys.argv[1]
            sim_time = datetime.fromisoformat(sim_time_str)
            print(f"🔬 进入模拟模式，模拟时间: {sim_time.strftime('%Y-%m-%d %H:%M:%S')}")

            sender = WarehouseReportSender(config)

            # 使用模拟时间处理数据
            status_data = sender.excel_processor.process_warehouse_status_data(target_date=sim_time)
            if not status_data:
                print("❌ 模拟数据处理失败，请检查Excel中是否存在该日期的数据。")
                return

            shipping_data = status_data
            pending_order_list = sender.excel_processor.process_pending_orders_file()

            # 动态判断阈值
            report_hour_threshold = config.get('report_time_threshold_hour', 21)
            is_after_threshold = sim_time.hour >= report_hour_threshold

            # 生成并打印主报告
            print(f"\n--- 预览：主报告 (报告阈值: {report_hour_threshold}:00, 是否达到: {'是' if is_after_threshold else '否'}) ---")
            main_report_md = sender.generate_main_report(status_data, shipping_data, is_after_threshold, bool(pending_order_list))
            print(main_report_md)

            # 计算并打印奖惩报告
            reward_data = sender.calculate_reward_penalty(status_data)
            if reward_data and is_after_threshold:
                print("\n--- 预览：奖惩报告 ---")
                reward_report = sender.generate_reward_penalty_report(reward_data)
                print(reward_report)

            print("\n✅ 模拟完成。以上是报告预览，未执行任何发送或写入操作。")

        except ValueError:
            print(f"❌ 无效的日期时间格式: '{sys.argv[1]}'. 请使用ISO格式，例如: YYYY-MM-DDTHH:MM:SS")
        except Exception as e:
            print(f"❌ 模拟模式执行失败: {str(e)}")
        return

    # 模式四：正常发送模式
    sender = WarehouseReportSender(config)
    sender.send_report()


if __name__ == "__main__":
    main() 