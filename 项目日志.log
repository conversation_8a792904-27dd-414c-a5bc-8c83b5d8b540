2025-08-02 17:31:32 - INFO - 日志记录已初始化，stdout和stderr现已重定向到日志文件。
2025-08-02 17:31:32 - INFO - 🏭 仓库发货数据钉钉通知程序
2025-08-02 17:31:32 - INFO - ========================================
2025-08-02 17:31:32 - INFO - 🔑 正在获取钉钉access_token...
2025-08-02 17:31:32 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-08-02 17:31:33 - INFO - ✅ 获取access_token成功，有效期: 7200秒
2025-08-02 17:31:33 - INFO - 🔧 钉钉表格工具初始化 - 测试环境
2025-08-02 17:31:33 - INFO - 📋 表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-08-02 17:31:33 - INFO - 📄 工作表1: 仓库绩效明细
2025-08-02 17:31:33 - INFO - 📄 工作表2: 仓库绩效汇总
2025-08-02 17:31:33 - INFO - ✅ 获取钉钉访问令牌成功
2025-08-02 17:31:33 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-08-02 17:31:33 - INFO - ✅ Excel处理器初始化完成. 数据目录: D:/仓库发货数据
2025-08-02 17:31:33 - INFO - 🚀 开始生成并发送仓库报告...
2025-08-02 17:31:33 - INFO - 🏭 开始处理最新的数据...
2025-08-02 17:31:33 - INFO - 📊 开始处理最新的仓库状态数据文件...
2025-08-02 17:31:33 - INFO - ✅ 找到最新的 '各部门绩效数据' 文件: 各部门绩效数据.xlsx
2025-08-02 17:31:33 - INFO - - 成功读取 '各部门绩效数据.xlsx' 的 '义乌仓' sheet页
2025-08-02 17:31:33 - INFO - - ℹ️ 在Excel中未找到今天 (2025-08-02) 的任何记录, 尝试查找昨天的数据...
2025-08-02 17:31:33 - INFO - - ℹ️ 在Excel中未找到今天 (2025-08-02) 或昨天23点后的任何记录。
2025-08-02 17:31:33 - INFO - 📊 开始处理待处理订单文件...
2025-08-02 17:31:33 - INFO - ⚠️ 在目录 'D:/仓库发货数据' 中未找到包含'待处理订单'关键词的Excel文件
2025-08-02 17:31:33 - INFO - ❌ 未能从Excel中获取最新的仓库状态数据，中止报告生成。
2025-08-02 17:31:33 - INFO - --- 阶段三：开始生成月度汇总 ---
2025-08-02 17:31:33 - INFO - 🚀 开始自动运行月度汇总流程...
2025-08-02 17:31:33 - INFO - ✅ 配置文件加载成功: config.json
2025-08-02 17:31:33 - INFO - 🔧 钉钉表格工具初始化 - 测试环境
2025-08-02 17:31:33 - INFO - 📋 表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-08-02 17:31:33 - INFO - 📄 工作表1: 仓库绩效明细
2025-08-02 17:31:33 - INFO - 📄 工作表2: 仓库绩效汇总
2025-08-02 17:31:33 - INFO - ✅ 获取钉钉访问令牌成功
2025-08-02 17:31:33 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-08-02 17:31:33 - INFO - ✅ 钉钉表格工具初始化完成
2025-08-02 17:31:33 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-08-02 17:31:34 - INFO - 📋 使用工作表: 仓库绩效明细 (kgqie6hm)
2025-08-02 17:31:34 - INFO - 🔍 开始读取仓库绩效明细数据...
2025-08-02 17:31:34 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-08-02 17:31:34 - INFO - 📊 读取到 500 行原始数据
2025-08-02 17:31:34 - INFO - ✅ 成功解析 6 条有效数据记录
2025-08-02 17:31:34 - INFO - 📊 读取到 6 条有效数据记录
2025-08-02 17:31:34 - INFO - 📊 开始生成月度汇总...
2025-08-02 17:31:34 - INFO - 📅 生成了 1 个月的汇总数据
2025-08-02 17:31:34 - INFO - 📝 开始写入Sheet2...
2025-08-02 17:31:34 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-08-02 17:31:35 - INFO - 📋 开始写入仓库绩效汇总: st-3a20f016-86121
2025-08-02 17:31:35 - INFO - 📝 准备写入数据...
2025-08-02 17:31:35 - INFO - 📝 准备 2025年07月数据汇总 数据...
2025-08-02 17:31:35 - INFO - 📊 开始写入 9 行数据到仓库绩效汇总...
2025-08-02 17:31:35 - INFO - 🔍 调试信息:
2025-08-02 17:31:35 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE/sheets/st-3a20f016-86121/ranges/A1:L9?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-08-02 17:31:35 - INFO - 数据: [['2025年07月数据汇总', '', '', '', '', '', '', '', '', '', '', ''], ['日期', '待补货品数', '待补货订单', '待处理品数', '待处理订单', '现货订单', '定制订单', '发货重量', '已审核定制', '已审核现货', '生产进度', '奖惩金额'], ['07-16', '1', '1', '6', '6', '1306', '453', '100875.0', '205', '2730', '未完成：0/9，已完成：9/9', '+35元'], ['07-18', '0', '0', '0', '0', '2644', '186', '7829.78', '164', '2688', '未完成：5/13，已完成：8/13', '+35元'], ['07-18', '0', '0', '0', '0', '2644', '186', '7829.78', '164', '2688', '未完成：5/13，已完成：8/13', '+35元'], ['07-18', '0', '0', '0', '0', '0', '0', '0.0', '208', '2803', '未完成：3/8，已完成：5/8', '+35元'], ['07-18', '9', '7', '13', '6', '0', '0', '0.0', '208', '2803', '未完成：3/8，已完成：5/8', '+35元'], ['07-18', '9', '7', '13', '6', '0', '0', '0.0', '208', '2803', '未完成：3/8，已完成：5/8', '+35元'], ['', '', '', '', '', '', '', '', '', '', '', '']]
2025-08-02 17:31:35 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-08-02 17:31:35 - INFO - 响应状态码: 200
2025-08-02 17:31:35 - INFO - 响应内容: {"a1Notation":"A1:L9"}
2025-08-02 17:31:35 - INFO - ✅ 成功写入数据到工作表 st-3a20f016-86121!A1:L9
2025-08-02 17:31:35 - INFO - ✅ 数据写入成功
2025-08-02 17:31:35 - INFO - 🎨 应用格式设置...
2025-08-02 17:31:35 - INFO - 📐 设置所有数据居中对齐...
2025-08-02 17:31:35 - INFO - 🔍 格式设置调试:
2025-08-02 17:31:35 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE/sheets/st-3a20f016-86121/ranges/A1:L9?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-08-02 17:31:35 - INFO - 格式数据: {'horizontalAlignments': [['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center']], 'verticalAlignments': [['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle']]}
2025-08-02 17:31:35 - INFO - 响应状态码: 200
2025-08-02 17:31:35 - INFO - 响应内容: {"a1Notation":"A1:L9"}
2025-08-02 17:31:35 - INFO - 🔗 合并标题行: A1:L1
2025-08-02 17:31:35 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-08-02 17:31:35 - INFO - ✅ 成功合并单元格: A1:L1
2025-08-02 17:31:35 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-08-02 17:31:36 - INFO - ✅ 成功设置合并单元格的值和格式: A1 = '2025年07月数据汇总'
2025-08-02 17:31:36 - INFO - ✅ 成功合并标题: 2025年07月数据汇总
2025-08-02 17:31:36 - INFO - 🎉 月度汇总数据已成功写入Sheet2！
2025-08-02 17:31:36 - INFO - 🎉 报告流程处理完成.
