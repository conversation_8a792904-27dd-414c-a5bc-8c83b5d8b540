2025-07-18 10:10:19 - INFO - 日志记录已初始化，stdout和stderr现已重定向到日志文件。
2025-07-18 10:10:19 - INFO - 🏭 仓库发货数据钉钉通知程序
2025-07-18 10:10:19 - INFO - ========================================
2025-07-18 10:10:19 - INFO - 🔄 重新执行模式：处理日期 2025-07-17 的完整流程
2025-07-18 10:10:19 - INFO - 📅 目标时间设定为: 2025-07-17 23:00:00
2025-07-18 10:10:19 - INFO - ⚠️  将按23点后的完整流程执行（群通知+个人通知+表格写入）
2025-07-18 10:10:19 - INFO - 🔑 正在获取钉钉access_token...
2025-07-18 10:10:19 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:10:19 - INFO - ✅ 获取access_token成功，有效期: 7200秒
2025-07-18 10:10:19 - INFO - 🔧 钉钉表格工具初始化 - 正式环境
2025-07-18 10:10:19 - INFO - 📋 表格ID: mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq
2025-07-18 10:10:19 - INFO - 📄 工作表1: 仓库绩效明细
2025-07-18 10:10:19 - INFO - 📄 工作表2: 仓库绩效汇总
2025-07-18 10:10:19 - INFO - ✅ 获取钉钉访问令牌成功
2025-07-18 10:10:19 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq
2025-07-18 10:10:19 - INFO - ✅ Excel处理器初始化完成. 数据目录: D:/仓库发货数据
2025-07-18 10:10:19 - INFO - 🏭 开始处理日期为 2025-07-17 的数据（强制完整流程）...
2025-07-18 10:10:19 - INFO - 📊 开始处理最新的仓库状态数据文件...
2025-07-18 10:10:19 - INFO - ✅ 找到最新的 '各部门绩效数据' 文件: 各部门绩效数据.xlsx
2025-07-18 10:10:19 - INFO - - 成功读取 '各部门绩效数据.xlsx' 的 '义乌仓' sheet页
2025-07-18 10:10:19 - ERROR - E:\RPA项目\仓库发货数据拉取\excel_processor.py:360: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
  df[time_col] = pd.to_datetime(df[time_col], errors='coerce')
2025-07-18 10:10:19 - INFO - - ℹ️ 在Excel中未找到指定日期 (2025-07-17) 的任何记录。
2025-07-18 10:10:19 - INFO - ❌ 未能从Excel中获取日期为 2025-07-17 的仓库状态数据，跳过处理。
2025-07-18 10:10:19 - INFO - ❌ 日期 2025-07-17 的流程执行失败，请检查数据是否存在。
2025-07-18 10:12:53 - INFO - 日志记录已初始化，stdout和stderr现已重定向到日志文件。
2025-07-18 10:12:53 - INFO - 🏭 仓库发货数据钉钉通知程序
2025-07-18 10:12:53 - INFO - ========================================
2025-07-18 10:12:53 - INFO - 🔄 重新执行模式：处理日期 2025-07-17 的完整流程
2025-07-18 10:12:53 - INFO - 📅 目标时间设定为: 2025-07-17 23:00:00
2025-07-18 10:12:53 - INFO - ⚠️  将按23点后的完整流程执行（群通知+个人通知+表格写入）
2025-07-18 10:12:53 - INFO - 🔑 正在获取钉钉access_token...
2025-07-18 10:12:53 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:12:53 - INFO - ✅ 获取access_token成功，有效期: 7200秒
2025-07-18 10:12:53 - INFO - 🔧 钉钉表格工具初始化 - 正式环境
2025-07-18 10:12:53 - INFO - 📋 表格ID: mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq
2025-07-18 10:12:53 - INFO - 📄 工作表1: 仓库绩效明细
2025-07-18 10:12:53 - INFO - 📄 工作表2: 仓库绩效汇总
2025-07-18 10:12:53 - INFO - ✅ 获取钉钉访问令牌成功
2025-07-18 10:12:53 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq
2025-07-18 10:12:53 - INFO - ✅ Excel处理器初始化完成. 数据目录: D:/仓库发货数据
2025-07-18 10:12:53 - INFO - 🏭 开始处理日期为 2025-07-17 的数据（强制完整流程）...
2025-07-18 10:12:53 - INFO - 📊 开始处理最新的仓库状态数据文件...
2025-07-18 10:12:53 - INFO - ✅ 找到最新的 '各部门绩效数据' 文件: 各部门绩效数据.xlsx
2025-07-18 10:12:53 - INFO - - 成功读取 '各部门绩效数据.xlsx' 的 '义乌仓' sheet页
2025-07-18 10:12:53 - ERROR - E:\RPA项目\仓库发货数据拉取\excel_processor.py:361: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
  df[time_col] = pd.to_datetime(df[time_col], errors='coerce')
2025-07-18 10:12:53 - INFO - - 🔄 发现 24 个无法解析的日期，尝试中文格式解析...
2025-07-18 10:12:53 - INFO - - ℹ️ 在Excel中未找到指定日期 (2025-07-17) 的任何记录。
2025-07-18 10:12:53 - INFO - ❌ 未能从Excel中获取日期为 2025-07-17 的仓库状态数据，跳过处理。
2025-07-18 10:12:53 - INFO - ❌ 日期 2025-07-17 的流程执行失败，请检查数据是否存在。
2025-07-18 10:14:00 - INFO - 日志记录已初始化，stdout和stderr现已重定向到日志文件。
2025-07-18 10:14:00 - INFO - 🏭 仓库发货数据钉钉通知程序
2025-07-18 10:14:00 - INFO - ========================================
2025-07-18 10:14:00 - INFO - 🔄 重新执行模式：处理日期 2025-07-17 的完整流程
2025-07-18 10:14:00 - INFO - 📅 目标时间设定为: 2025-07-17 23:00:00
2025-07-18 10:14:00 - INFO - ⚠️  将按23点后的完整流程执行（群通知+个人通知+表格写入）
2025-07-18 10:14:00 - INFO - 🔑 正在获取钉钉access_token...
2025-07-18 10:14:01 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:14:02 - INFO - ✅ 获取access_token成功，有效期: 7200秒
2025-07-18 10:14:02 - INFO - 🔧 钉钉表格工具初始化 - 正式环境
2025-07-18 10:14:02 - INFO - 📋 表格ID: mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq
2025-07-18 10:14:02 - INFO - 📄 工作表1: 仓库绩效明细
2025-07-18 10:14:02 - INFO - 📄 工作表2: 仓库绩效汇总
2025-07-18 10:14:03 - INFO - ✅ 获取钉钉访问令牌成功
2025-07-18 10:14:03 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq
2025-07-18 10:14:03 - INFO - ✅ Excel处理器初始化完成. 数据目录: D:/仓库发货数据
2025-07-18 10:14:03 - INFO - 🏭 开始处理日期为 2025-07-17 的数据（强制完整流程）...
2025-07-18 10:14:03 - INFO - 📊 开始处理最新的仓库状态数据文件...
2025-07-18 10:14:03 - INFO - ✅ 找到最新的 '各部门绩效数据' 文件: 各部门绩效数据.xlsx
2025-07-18 10:14:04 - INFO - - 成功读取 '各部门绩效数据.xlsx' 的 '义乌仓' sheet页
2025-07-18 10:14:04 - ERROR - E:\RPA项目\仓库发货数据拉取\excel_processor.py:361: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
  df[time_col] = pd.to_datetime(df[time_col], errors='coerce')
2025-07-18 10:14:04 - INFO - - 🔄 发现 24 个无法解析的日期，尝试中文格式解析...
2025-07-18 10:14:04 - INFO - - 📋 无法解析的日期样本: ['NaT', 'NaT', 'NaT']
2025-07-18 10:14:04 - INFO - - ✅ 成功解析 0 个中文日期格式
2025-07-18 10:14:04 - INFO - - ⚠️ 仍有 24 个日期无法解析，样本: ['NaT', 'NaT']
2025-07-18 10:14:04 - INFO - - ℹ️ 在Excel中未找到指定日期 (2025-07-17) 的任何记录。
2025-07-18 10:14:04 - INFO - ❌ 未能从Excel中获取日期为 2025-07-17 的仓库状态数据，跳过处理。
2025-07-18 10:14:04 - INFO - ❌ 日期 2025-07-17 的流程执行失败，请检查数据是否存在。
2025-07-18 10:15:21 - INFO - 日志记录已初始化，stdout和stderr现已重定向到日志文件。
2025-07-18 10:15:21 - INFO - 🏭 仓库发货数据钉钉通知程序
2025-07-18 10:15:21 - INFO - ========================================
2025-07-18 10:15:21 - INFO - 🔄 重新执行模式：处理日期 2025-07-17 的完整流程
2025-07-18 10:15:21 - INFO - 📅 目标时间设定为: 2025-07-17 23:00:00
2025-07-18 10:15:21 - INFO - ⚠️  将按23点后的完整流程执行（群通知+个人通知+表格写入）
2025-07-18 10:15:21 - INFO - 🔑 正在获取钉钉access_token...
2025-07-18 10:15:22 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:15:23 - INFO - ✅ 获取access_token成功，有效期: 7200秒
2025-07-18 10:15:23 - INFO - 🔧 钉钉表格工具初始化 - 正式环境
2025-07-18 10:15:23 - INFO - 📋 表格ID: mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq
2025-07-18 10:15:23 - INFO - 📄 工作表1: 仓库绩效明细
2025-07-18 10:15:23 - INFO - 📄 工作表2: 仓库绩效汇总
2025-07-18 10:15:23 - INFO - ✅ 获取钉钉访问令牌成功
2025-07-18 10:15:23 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq
2025-07-18 10:15:23 - INFO - ✅ Excel处理器初始化完成. 数据目录: D:/仓库发货数据
2025-07-18 10:15:23 - INFO - 🏭 开始处理日期为 2025-07-17 的数据（强制完整流程）...
2025-07-18 10:15:23 - INFO - 📊 开始处理最新的仓库状态数据文件...
2025-07-18 10:15:23 - INFO - ✅ 找到最新的 '各部门绩效数据' 文件: 各部门绩效数据.xlsx
2025-07-18 10:15:23 - INFO - - 成功读取 '各部门绩效数据.xlsx' 的 '义乌仓' sheet页
2025-07-18 10:15:23 - ERROR - E:\RPA项目\仓库发货数据拉取\excel_processor.py:361: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
  df[time_col] = pd.to_datetime(df[time_col], errors='coerce')
2025-07-18 10:15:23 - INFO - - 🔄 发现 24 个无法解析的日期，尝试中文格式解析...
2025-07-18 10:15:23 - INFO - - 🔄 重新读取原始Excel文件以获取未转换的日期数据...
2025-07-18 10:15:23 - INFO - - ⚠️ 重新读取原始文件失败: name 'file_path' is not defined
2025-07-18 10:15:23 - INFO - - 📋 无法解析的日期样本: [NaT, NaT, NaT]
2025-07-18 10:15:23 - INFO - - ✅ 成功解析 0 个中文日期格式
2025-07-18 10:15:23 - INFO - - ⚠️ 仍有 24 个日期无法解析，样本: [NaT, NaT]
2025-07-18 10:15:23 - INFO - - ℹ️ 在Excel中未找到指定日期 (2025-07-17) 的任何记录。
2025-07-18 10:15:23 - INFO - ❌ 未能从Excel中获取日期为 2025-07-17 的仓库状态数据，跳过处理。
2025-07-18 10:15:23 - INFO - ❌ 日期 2025-07-17 的流程执行失败，请检查数据是否存在。
2025-07-18 10:16:43 - INFO - 日志记录已初始化，stdout和stderr现已重定向到日志文件。
2025-07-18 10:16:43 - INFO - 🏭 仓库发货数据钉钉通知程序
2025-07-18 10:16:43 - INFO - ========================================
2025-07-18 10:16:43 - INFO - 🔄 重新执行模式：处理日期 2025-07-17 的完整流程
2025-07-18 10:16:43 - INFO - 📅 目标时间设定为: 2025-07-17 23:00:00
2025-07-18 10:16:43 - INFO - ⚠️  将按23点后的完整流程执行（群通知+个人通知+表格写入）
2025-07-18 10:16:43 - INFO - 🔑 正在获取钉钉access_token...
2025-07-18 10:16:43 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:43 - INFO - ✅ 获取access_token成功，有效期: 7200秒
2025-07-18 10:16:43 - INFO - 🔧 钉钉表格工具初始化 - 正式环境
2025-07-18 10:16:43 - INFO - 📋 表格ID: mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq
2025-07-18 10:16:43 - INFO - 📄 工作表1: 仓库绩效明细
2025-07-18 10:16:43 - INFO - 📄 工作表2: 仓库绩效汇总
2025-07-18 10:16:44 - INFO - ✅ 获取钉钉访问令牌成功
2025-07-18 10:16:44 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq
2025-07-18 10:16:44 - INFO - ✅ Excel处理器初始化完成. 数据目录: D:/仓库发货数据
2025-07-18 10:16:44 - INFO - 🏭 开始处理日期为 2025-07-17 的数据（强制完整流程）...
2025-07-18 10:16:44 - INFO - 📊 开始处理最新的仓库状态数据文件...
2025-07-18 10:16:44 - INFO - ✅ 找到最新的 '各部门绩效数据' 文件: 各部门绩效数据.xlsx
2025-07-18 10:16:44 - INFO - - 成功读取 '各部门绩效数据.xlsx' 的 '义乌仓' sheet页
2025-07-18 10:16:44 - ERROR - E:\RPA项目\仓库发货数据拉取\excel_processor.py:361: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
  df[time_col] = pd.to_datetime(df[time_col], errors='coerce')
2025-07-18 10:16:44 - INFO - - 🔄 发现 24 个无法解析的日期，尝试中文格式解析...
2025-07-18 10:16:44 - INFO - - 🔄 重新读取原始Excel文件以获取未转换的日期数据...
2025-07-18 10:16:44 - INFO - - 📋 原始数据类型: <class 'str'>
2025-07-18 10:16:44 - INFO - - 📋 无法解析的日期样本: ['2025年06月27日 09:58:50', '2025年06月27日 10:14:11', '2025年06月28日 09:39:58']
2025-07-18 10:16:44 - INFO - - ✅ 成功解析 24 个中文日期格式
2025-07-18 10:16:44 - INFO - - ✅ 找到匹配的数据行，开始提取...
2025-07-18 10:16:44 - INFO - - 提取到数据，报告日期为: 2025/07/17, 数据时间戳: 2025-07-17 23:07:55
2025-07-18 10:16:44 - INFO - 📊 开始处理待处理订单文件...
2025-07-18 10:16:44 - INFO - ⚠️ 在目录 'D:/仓库发货数据' 中未找到包含'待处理订单'关键词的Excel文件
2025-07-18 10:16:44 - INFO - 🕚 强制按23点后完整流程执行：群通知 + 个人通知 + 表格写入
2025-07-18 10:16:44 - INFO - 📤 正在发送主报告到群...
2025-07-18 10:16:44 - INFO - ✅ 钉钉消息发送成功
2025-07-18 10:16:44 - INFO - ✅ 主报告发送成功！
2025-07-18 10:16:44 - INFO - 📊 奖惩计算基础数据：
2025-07-18 10:16:44 - INFO - 已审核-定制：164单
2025-07-18 10:16:44 - INFO - 已审核-现货：2688单
2025-07-18 10:16:44 - INFO - 已审核订单总计：2852单 (定制+现货)
2025-07-18 10:16:44 - INFO - 待补货订单：0单
2025-07-18 10:16:44 - INFO - 待处理订单：0单
2025-07-18 10:16:45 - INFO - 📤 正在发送奖惩报告到个人...
2025-07-18 10:16:45 - INFO - 📤 当前环境: 正式环境
2025-07-18 10:16:45 - INFO - 📤 发送目标: 宋继文（义乌仓储部） (userId: 1529202823554123)
2025-07-18 10:16:45 - INFO - 📤 使用机器人发送个人消息给用户 1529202823554123
2025-07-18 10:16:45 - INFO - 📋 消息标题: 奖惩计算结果
2025-07-18 10:16:45 - INFO - 📄 消息内容: 奖惩计算结果

基础数据：

已审核订单：2,852单

待补货订单：0单

待处理订单：0单

待补货奖惩（10元规则）：
条件：已审核≤4000且待补货≤15
结果：奖励 +10元

待处理奖惩（...
2025-07-18 10:16:45 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:45 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:45 - INFO - 📊 发送结果: {'flowControlledStaffIdList': [], 'invalidStaffIdList': [], 'processQueryKey': 'mDjqvoVMi+S6XjiLDzMkSzUytixDWcTh7K97exQPMlw='}
2025-07-18 10:16:45 - INFO - ✅ 机器人消息发送成功
2025-07-18 10:16:45 - INFO - 任务查询键: mDjqvoVMi+S6XjiLDzMkSzUytixDWcTh7K97exQPMlw=
2025-07-18 10:16:45 - INFO - ✅ 奖惩报告已发送给宋继文（义乌仓储部） - 正式环境
2025-07-18 10:16:45 - INFO - 📊 正在将报告块写入到钉钉表格...
2025-07-18 10:16:45 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:46 - INFO - 📝 追加日期为 2025/07/17 的历史数据到表格...
2025-07-18 10:16:46 - INFO - 🔍 配置文件无记录，开始全表扫描最后一行...
2025-07-18 10:16:46 - INFO - 🔍 正在高效查找最后一行数据（一次性读取最多 1000 行）...
2025-07-18 10:16:46 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:47 - INFO - 📍 找到最后数据行: 225
2025-07-18 10:16:47 - INFO - 📍 找到最后数据行: 225，下一个数据块将从第 227 行开始（留出1行空行分隔）
2025-07-18 10:16:47 - INFO - 🔗 正在合并分隔空行: A226:C226
2025-07-18 10:16:47 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:47 - INFO - ✅ 成功合并单元格: A226:C226
2025-07-18 10:16:47 - INFO - 🔍 调试信息:
2025-07-18 10:16:47 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/mExel2BLV5xwwjd3iEe5ALmmWgk9rpMq/sheets/kgqie6hm/ranges/A227:C239?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-07-18 10:16:47 - INFO - 数据: [['数据统计时间', '2025/07/18 10:16:47', ''], ['仓库状态数据', '', ''], ['类型', '品数', '订单数'], ['待补货', '0', '0'], ['待处理', '0', '0'], ['生产单完成进度', '', ''], ['已审核-定制', '', ''], ['已审核-现货', '', ''], ['发货数据', '', ''], ['现货订单数', '', ''], ['现货订单发货总重量', '', ''], ['定制订单数', '', ''], ['定制订单发货总重量', '', '']]
2025-07-18 10:16:47 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:47 - INFO - 响应状态码: 200
2025-07-18 10:16:47 - INFO - 响应内容: {"a1Notation":"A227:C239"}
2025-07-18 10:16:47 - INFO - ✅ 成功写入数据到工作表 kgqie6hm!A227:C239
2025-07-18 10:16:47 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:48 - INFO - ✅ 成功设置综合格式: A227:C239
2025-07-18 10:16:48 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:48 - INFO - ✅ 成功合并单元格: B227:C227
2025-07-18 10:16:48 - INFO - 🕐 检测到时间格式，设置为文本格式: 2025/07/18 10:16:47
2025-07-18 10:16:48 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:48 - INFO - ✅ 成功设置合并单元格的值和格式: B227 = '2025/07/18 10:16:47'
2025-07-18 10:16:48 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:48 - INFO - ✅ 成功合并单元格: A228:C228
2025-07-18 10:16:48 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:48 - INFO - ✅ 成功设置合并单元格的值和格式: A228 = '仓库状态数据'
2025-07-18 10:16:48 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:48 - INFO - ✅ 成功合并单元格: B232:C232
2025-07-18 10:16:49 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:49 - INFO - ✅ 成功设置合并单元格的值和格式: B232 = '未完成：5/13，已完成：8/13'
2025-07-18 10:16:49 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:49 - INFO - ✅ 成功合并单元格: B233:C233
2025-07-18 10:16:49 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:49 - INFO - ✅ 成功设置合并单元格的值和格式: B233 = '164'
2025-07-18 10:16:49 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:49 - INFO - ✅ 成功合并单元格: B234:C234
2025-07-18 10:16:49 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:49 - INFO - ✅ 成功设置合并单元格的值和格式: B234 = '2688'
2025-07-18 10:16:50 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:50 - INFO - ✅ 成功合并单元格: A235:C235
2025-07-18 10:16:50 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:50 - INFO - ✅ 成功设置合并单元格的值和格式: A235 = '发货数据'
2025-07-18 10:16:50 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:50 - INFO - ✅ 成功合并单元格: B236:C236
2025-07-18 10:16:50 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:50 - INFO - ✅ 成功设置合并单元格的值和格式: B236 = '2644'
2025-07-18 10:16:50 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:50 - INFO - ✅ 成功合并单元格: B237:C237
2025-07-18 10:16:51 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:51 - INFO - ✅ 成功设置合并单元格的值和格式: B237 = '4652.41kg'
2025-07-18 10:16:51 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:51 - INFO - ✅ 成功合并单元格: B238:C238
2025-07-18 10:16:51 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:51 - INFO - ✅ 成功设置合并单元格的值和格式: B238 = '186'
2025-07-18 10:16:51 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:51 - INFO - ✅ 成功合并单元格: B239:C239
2025-07-18 10:16:51 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:16:51 - INFO - ✅ 成功设置合并单元格的值和格式: B239 = '3177.37kg'
2025-07-18 10:16:51 - INFO - ℹ️ 钉钉表格API不支持边框设置，跳过边框设置: A227:C239
2025-07-18 10:16:51 - INFO - ✅ 成功追加仓库报告块到第 227-239 行
2025-07-18 10:16:51 - INFO - ✅ 历史数据成功写入钉钉表格！
2025-07-18 10:16:51 - INFO - 📝 已更新正式环境配置文件，记录最后一行: 239
2025-07-18 10:16:51 - INFO - 🎉 日期 2025-07-17 的完整流程处理完成！
2025-07-18 10:16:51 - INFO - ✅ 日期 2025-07-17 的完整流程重新执行成功！
2025-07-18 10:20:00 - INFO - 日志记录已初始化，stdout和stderr现已重定向到日志文件。
2025-07-18 10:20:00 - INFO - 🏭 仓库发货数据钉钉通知程序
2025-07-18 10:20:00 - INFO - ========================================
2025-07-18 10:20:00 - INFO - 🔄 重新执行模式：处理日期 2025-07-17 的完整流程
2025-07-18 10:20:00 - INFO - 📅 目标时间设定为: 2025-07-17 23:00:00
2025-07-18 10:20:00 - INFO - ⚠️  将按23点后的完整流程执行（群通知+个人通知+表格写入）
2025-07-18 10:20:00 - INFO - 🔑 正在获取钉钉access_token...
2025-07-18 10:20:00 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:00 - INFO - ✅ 获取access_token成功，有效期: 7200秒
2025-07-18 10:20:00 - INFO - 🔧 钉钉表格工具初始化 - 测试环境
2025-07-18 10:20:00 - INFO - 📋 表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 10:20:00 - INFO - 📄 工作表1: 仓库绩效明细
2025-07-18 10:20:00 - INFO - 📄 工作表2: 仓库绩效汇总
2025-07-18 10:20:00 - INFO - ✅ 获取钉钉访问令牌成功
2025-07-18 10:20:00 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 10:20:00 - INFO - ✅ Excel处理器初始化完成. 数据目录: D:/仓库发货数据
2025-07-18 10:20:00 - INFO - 🏭 开始处理日期为 2025-07-17 的数据（强制完整流程）...
2025-07-18 10:20:00 - INFO - 📊 开始处理最新的仓库状态数据文件...
2025-07-18 10:20:00 - INFO - ✅ 找到最新的 '各部门绩效数据' 文件: 各部门绩效数据.xlsx
2025-07-18 10:20:00 - INFO - - 成功读取 '各部门绩效数据.xlsx' 的 '义乌仓' sheet页
2025-07-18 10:20:00 - ERROR - E:\RPA项目\仓库发货数据拉取\excel_processor.py:361: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
  df[time_col] = pd.to_datetime(df[time_col], errors='coerce')
2025-07-18 10:20:00 - INFO - - 🔄 发现 24 个无法解析的日期，尝试中文格式解析...
2025-07-18 10:20:00 - INFO - - 🔄 重新读取原始Excel文件以获取未转换的日期数据...
2025-07-18 10:20:00 - INFO - - 📋 原始数据类型: <class 'str'>
2025-07-18 10:20:00 - INFO - - 📋 无法解析的日期样本: ['2025年06月27日 09:58:50', '2025年06月27日 10:14:11', '2025年06月28日 09:39:58']
2025-07-18 10:20:00 - INFO - - ✅ 成功解析 24 个中文日期格式
2025-07-18 10:20:00 - INFO - - ✅ 找到匹配的数据行，开始提取...
2025-07-18 10:20:00 - INFO - - 提取到数据，报告日期为: 2025/07/17, 数据时间戳: 2025-07-17 23:07:55
2025-07-18 10:20:00 - INFO - 📊 开始处理待处理订单文件...
2025-07-18 10:20:00 - INFO - ⚠️ 在目录 'D:/仓库发货数据' 中未找到包含'待处理订单'关键词的Excel文件
2025-07-18 10:20:00 - INFO - 🕚 强制按23点后完整流程执行：群通知 + 个人通知 + 表格写入
2025-07-18 10:20:00 - INFO - 📤 正在发送主报告到群...
2025-07-18 10:20:01 - INFO - ✅ 钉钉消息发送成功
2025-07-18 10:20:01 - INFO - ✅ 主报告发送成功！
2025-07-18 10:20:01 - INFO - 📊 奖惩计算基础数据：
2025-07-18 10:20:01 - INFO - 已审核-定制：164单
2025-07-18 10:20:01 - INFO - 已审核-现货：2688单
2025-07-18 10:20:01 - INFO - 已审核订单总计：2852单 (定制+现货)
2025-07-18 10:20:01 - INFO - 待补货订单：0单
2025-07-18 10:20:01 - INFO - 待处理订单：0单
2025-07-18 10:20:02 - INFO - 📤 正在发送奖惩报告到个人...
2025-07-18 10:20:02 - INFO - 📤 当前环境: 测试环境
2025-07-18 10:20:02 - INFO - 📤 发送目标: 杜陶祎（信息部） (userId: 01415968542326653172)
2025-07-18 10:20:02 - INFO - 📤 使用机器人发送个人消息给用户 01415968542326653172
2025-07-18 10:20:02 - INFO - 📋 消息标题: 奖惩计算结果
2025-07-18 10:20:02 - INFO - 📄 消息内容: 奖惩计算结果

基础数据：

已审核订单：2,852单

待补货订单：0单

待处理订单：0单

待补货奖惩（10元规则）：
条件：已审核≤4000且待补货≤15
结果：奖励 +10元

待处理奖惩（...
2025-07-18 10:20:02 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:02 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:02 - INFO - 📊 发送结果: {'flowControlledStaffIdList': [], 'invalidStaffIdList': [], 'processQueryKey': 'kwD78CfQlvm0uVOBpYYvN00KSIcmPppQRfmKBaatUjw='}
2025-07-18 10:20:02 - INFO - ✅ 机器人消息发送成功
2025-07-18 10:20:02 - INFO - 任务查询键: kwD78CfQlvm0uVOBpYYvN00KSIcmPppQRfmKBaatUjw=
2025-07-18 10:20:02 - INFO - ✅ 奖惩报告已发送给杜陶祎（信息部） - 测试环境
2025-07-18 10:20:02 - INFO - 📊 正在将报告块写入到钉钉表格...
2025-07-18 10:20:02 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:02 - INFO - 📝 追加日期为 2025/07/17 的历史数据到表格...
2025-07-18 10:20:02 - INFO - 🔍 配置文件无记录，开始全表扫描最后一行...
2025-07-18 10:20:02 - INFO - 🔍 正在高效查找最后一行数据（一次性读取最多 1000 行）...
2025-07-18 10:20:02 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:03 - INFO - 📍 找到最后数据行: 18
2025-07-18 10:20:03 - INFO - 📍 找到最后数据行: 18，下一个数据块将从第 20 行开始（留出1行空行分隔）
2025-07-18 10:20:03 - INFO - 🔗 正在合并分隔空行: A19:C19
2025-07-18 10:20:03 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:03 - INFO - ✅ 成功合并单元格: A19:C19
2025-07-18 10:20:03 - INFO - 🔍 调试信息:
2025-07-18 10:20:03 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE/sheets/kgqie6hm/ranges/A20:C37?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-07-18 10:20:03 - INFO - 数据: [['数据统计时间', '2025/07/18 10:20:03', ''], ['仓库状态数据', '', ''], ['类型', '品数', '订单数'], ['待补货', '0', '0'], ['待处理', '0', '0'], ['生产单完成进度', '', ''], ['已审核-定制', '', ''], ['已审核-现货', '', ''], ['发货数据', '', ''], ['现货订单数', '', ''], ['现货订单发货总重量', '', ''], ['定制订单数', '', ''], ['定制订单发货总重量', '', ''], ['奖惩计算', '', ''], ['待补货奖惩', '', ''], ['待处理奖惩', '', ''], ['总计奖惩', '', ''], ['奖惩计算结果', '', '']]
2025-07-18 10:20:04 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:04 - INFO - 响应状态码: 200
2025-07-18 10:20:04 - INFO - 响应内容: {"a1Notation":"A20:C37"}
2025-07-18 10:20:04 - INFO - ✅ 成功写入数据到工作表 kgqie6hm!A20:C37
2025-07-18 10:20:04 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:04 - INFO - ✅ 成功设置综合格式: A20:C37
2025-07-18 10:20:04 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:04 - INFO - ✅ 成功合并单元格: B20:C20
2025-07-18 10:20:04 - INFO - 🕐 检测到时间格式，设置为文本格式: 2025/07/18 10:20:03
2025-07-18 10:20:04 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:04 - INFO - ✅ 成功设置合并单元格的值和格式: B20 = '2025/07/18 10:20:03'
2025-07-18 10:20:04 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:05 - INFO - ✅ 成功合并单元格: A21:C21
2025-07-18 10:20:05 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:05 - INFO - ✅ 成功设置合并单元格的值和格式: A21 = '仓库状态数据'
2025-07-18 10:20:05 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:05 - INFO - ✅ 成功合并单元格: B25:C25
2025-07-18 10:20:05 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:05 - INFO - ✅ 成功设置合并单元格的值和格式: B25 = '未完成：5/13，已完成：8/13'
2025-07-18 10:20:05 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:05 - INFO - ✅ 成功合并单元格: B26:C26
2025-07-18 10:20:05 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:05 - INFO - ✅ 成功设置合并单元格的值和格式: B26 = '164'
2025-07-18 10:20:06 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:06 - INFO - ✅ 成功合并单元格: B27:C27
2025-07-18 10:20:06 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:06 - INFO - ✅ 成功设置合并单元格的值和格式: B27 = '2688'
2025-07-18 10:20:06 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:06 - INFO - ✅ 成功合并单元格: A28:C28
2025-07-18 10:20:06 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:06 - INFO - ✅ 成功设置合并单元格的值和格式: A28 = '发货数据'
2025-07-18 10:20:06 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:07 - INFO - ✅ 成功合并单元格: B29:C29
2025-07-18 10:20:07 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:07 - INFO - ✅ 成功设置合并单元格的值和格式: B29 = '2644'
2025-07-18 10:20:07 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:07 - INFO - ✅ 成功合并单元格: B30:C30
2025-07-18 10:20:07 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:07 - INFO - ✅ 成功设置合并单元格的值和格式: B30 = '4652.41kg'
2025-07-18 10:20:07 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:07 - INFO - ✅ 成功合并单元格: B31:C31
2025-07-18 10:20:07 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:08 - INFO - ✅ 成功设置合并单元格的值和格式: B31 = '186'
2025-07-18 10:20:08 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:08 - INFO - ✅ 成功合并单元格: B32:C32
2025-07-18 10:20:08 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:08 - INFO - ✅ 成功设置合并单元格的值和格式: B32 = '3177.37kg'
2025-07-18 10:20:08 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:08 - INFO - ✅ 成功合并单元格: A33:C33
2025-07-18 10:20:08 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:08 - INFO - ✅ 成功设置合并单元格的值和格式: A33 = '奖惩计算'
2025-07-18 10:20:08 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:09 - INFO - ✅ 成功合并单元格: B34:C34
2025-07-18 10:20:09 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:09 - INFO - ✅ 成功设置合并单元格的值和格式: B34 = '奖励 +10元'
2025-07-18 10:20:09 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:09 - INFO - ✅ 成功合并单元格: B35:C35
2025-07-18 10:20:09 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:09 - INFO - ✅ 成功设置合并单元格的值和格式: B35 = '奖励 +25元'
2025-07-18 10:20:09 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:09 - INFO - ✅ 成功合并单元格: B36:C36
2025-07-18 10:20:09 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:09 - INFO - ✅ 成功设置合并单元格的值和格式: B36 = '+35元'
2025-07-18 10:20:10 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:10 - INFO - ✅ 成功合并单元格: A37:C37
2025-07-18 10:20:10 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:20:10 - INFO - ✅ 成功设置合并单元格的值和格式: A37 = '已审核订单：2,852单 待补货订单：0单 待处理订单：0单 | 待补货奖惩（10元规则）：已审核≤4000且待补货≤15 结果：奖励 10元 | 待处理奖惩（25元规则）：已审核≤4000且待处理≤28（1%） 结果：奖励 25元 | 总计奖惩：+35元'
2025-07-18 10:20:10 - INFO - ℹ️ 钉钉表格API不支持边框设置，跳过边框设置: A20:C37
2025-07-18 10:20:10 - INFO - ✅ 成功追加仓库报告块到第 20-37 行
2025-07-18 10:20:10 - INFO - ✅ 历史数据成功写入钉钉表格！
2025-07-18 10:20:10 - INFO - 📝 已更新测试环境配置文件，记录最后一行: 37
2025-07-18 10:20:10 - INFO - 🎉 日期 2025-07-17 的完整流程处理完成！
2025-07-18 10:20:10 - INFO - ✅ 日期 2025-07-17 的完整流程重新执行成功！
2025-07-18 10:23:13 - INFO - 日志记录已初始化，stdout和stderr现已重定向到日志文件。
2025-07-18 10:23:13 - INFO - 🏭 仓库发货数据钉钉通知程序
2025-07-18 10:23:13 - INFO - ========================================
2025-07-18 10:23:13 - INFO - 🔄 重新执行模式：处理日期 2025-07-17 的完整流程
2025-07-18 10:23:13 - INFO - 📅 目标时间设定为: 2025-07-17 23:00:00
2025-07-18 10:23:13 - INFO - ⚠️  将按23点后的完整流程执行（群通知+个人通知+表格写入）
2025-07-18 10:23:13 - INFO - 🔑 正在获取钉钉access_token...
2025-07-18 10:23:13 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:13 - INFO - ✅ 获取access_token成功，有效期: 7200秒
2025-07-18 10:23:13 - INFO - 🔧 钉钉表格工具初始化 - 测试环境
2025-07-18 10:23:13 - INFO - 📋 表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 10:23:13 - INFO - 📄 工作表1: 仓库绩效明细
2025-07-18 10:23:13 - INFO - 📄 工作表2: 仓库绩效汇总
2025-07-18 10:23:13 - INFO - ✅ 获取钉钉访问令牌成功
2025-07-18 10:23:13 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 10:23:13 - INFO - ✅ Excel处理器初始化完成. 数据目录: D:/仓库发货数据
2025-07-18 10:23:13 - INFO - 🏭 开始处理日期为 2025-07-17 的数据（强制完整流程）...
2025-07-18 10:23:13 - INFO - 📊 开始处理最新的仓库状态数据文件...
2025-07-18 10:23:13 - INFO - ✅ 找到最新的 '各部门绩效数据' 文件: 各部门绩效数据.xlsx
2025-07-18 10:23:14 - INFO - - 成功读取 '各部门绩效数据.xlsx' 的 '义乌仓' sheet页
2025-07-18 10:23:14 - ERROR - E:\RPA项目\仓库发货数据拉取\excel_processor.py:361: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
  df[time_col] = pd.to_datetime(df[time_col], errors='coerce')
2025-07-18 10:23:14 - INFO - - 🔄 发现 24 个无法解析的日期，尝试中文格式解析...
2025-07-18 10:23:14 - INFO - - 🔄 重新读取原始Excel文件以获取未转换的日期数据...
2025-07-18 10:23:14 - INFO - - 📋 原始数据类型: <class 'str'>
2025-07-18 10:23:14 - INFO - - 📋 无法解析的日期样本: ['2025年06月27日 09:58:50', '2025年06月27日 10:14:11', '2025年06月28日 09:39:58']
2025-07-18 10:23:14 - INFO - - ✅ 成功解析 24 个中文日期格式
2025-07-18 10:23:14 - INFO - - ✅ 找到匹配的数据行，开始提取...
2025-07-18 10:23:14 - INFO - - 提取到数据，报告日期为: 2025/07/17, 数据时间戳: 2025-07-17 23:07:55
2025-07-18 10:23:14 - INFO - 📊 开始处理待处理订单文件...
2025-07-18 10:23:14 - INFO - ⚠️ 在目录 'D:/仓库发货数据' 中未找到包含'待处理订单'关键词的Excel文件
2025-07-18 10:23:14 - INFO - 🕚 强制按23点后完整流程执行：群通知 + 个人通知 + 表格写入
2025-07-18 10:23:14 - INFO - 📤 正在发送主报告到群...
2025-07-18 10:23:14 - INFO - ✅ 钉钉消息发送成功
2025-07-18 10:23:14 - INFO - ✅ 主报告发送成功！
2025-07-18 10:23:14 - INFO - 📊 奖惩计算基础数据：
2025-07-18 10:23:14 - INFO - 已审核-定制：164单
2025-07-18 10:23:14 - INFO - 已审核-现货：2688单
2025-07-18 10:23:14 - INFO - 已审核订单总计：2852单 (定制+现货)
2025-07-18 10:23:14 - INFO - 待补货订单：0单
2025-07-18 10:23:14 - INFO - 待处理订单：0单
2025-07-18 10:23:15 - INFO - 📤 正在发送奖惩报告到个人...
2025-07-18 10:23:15 - INFO - 📤 当前环境: 测试环境
2025-07-18 10:23:15 - INFO - 📤 发送目标: 杜陶祎（信息部） (userId: 01415968542326653172)
2025-07-18 10:23:15 - INFO - 📤 使用机器人发送个人消息给用户 01415968542326653172
2025-07-18 10:23:15 - INFO - 📋 消息标题: 奖惩计算结果
2025-07-18 10:23:15 - INFO - 📄 消息内容: 奖惩计算结果

基础数据：

已审核订单：2,852单

待补货订单：0单

待处理订单：0单

待补货奖惩（10元规则）：
条件：已审核≤4000且待补货≤15
结果：奖励 +10元

待处理奖惩（...
2025-07-18 10:23:15 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:15 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:15 - INFO - 📊 发送结果: {'flowControlledStaffIdList': [], 'invalidStaffIdList': [], 'processQueryKey': 'jRr91E4rCFiZg6OGYarPDGigHVn7l7z8czcewx7VpFg='}
2025-07-18 10:23:15 - INFO - ✅ 机器人消息发送成功
2025-07-18 10:23:15 - INFO - 任务查询键: jRr91E4rCFiZg6OGYarPDGigHVn7l7z8czcewx7VpFg=
2025-07-18 10:23:15 - INFO - ✅ 奖惩报告已发送给杜陶祎（信息部） - 测试环境
2025-07-18 10:23:15 - INFO - 📊 正在将报告块写入到钉钉表格...
2025-07-18 10:23:15 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:15 - INFO - 📝 追加日期为 2025/07/17 的历史数据到表格...
2025-07-18 10:23:15 - INFO - 🔍 配置文件无记录，开始全表扫描最后一行...
2025-07-18 10:23:15 - INFO - 🔍 正在高效查找最后一行数据（一次性读取最多 1000 行）...
2025-07-18 10:23:16 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:17 - INFO - 📍 找到最后数据行: 37
2025-07-18 10:23:17 - INFO - 📍 找到最后数据行: 37，下一个数据块将从第 39 行开始（留出1行空行分隔）
2025-07-18 10:23:17 - INFO - 🔗 正在合并分隔空行: A38:C38
2025-07-18 10:23:17 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:17 - INFO - ✅ 成功合并单元格: A38:C38
2025-07-18 10:23:17 - INFO - 🔍 调试信息:
2025-07-18 10:23:17 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE/sheets/kgqie6hm/ranges/A39:C56?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-07-18 10:23:17 - INFO - 数据: [['数据统计时间', '2025/07/18 10:23:17', ''], ['仓库状态数据', '', ''], ['类型', '品数', '订单数'], ['待补货', '0', '0'], ['待处理', '0', '0'], ['生产单完成进度', '', ''], ['已审核-定制', '', ''], ['已审核-现货', '', ''], ['发货数据', '', ''], ['现货订单数', '', ''], ['现货订单发货总重量', '', ''], ['定制订单数', '', ''], ['定制订单发货总重量', '', ''], ['奖惩计算', '', ''], ['待补货奖惩', '', ''], ['待处理奖惩', '', ''], ['总计奖惩', '', ''], ['奖惩计算结果', '', '']]
2025-07-18 10:23:17 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:17 - INFO - 响应状态码: 200
2025-07-18 10:23:17 - INFO - 响应内容: {"a1Notation":"A39:C56"}
2025-07-18 10:23:17 - INFO - ✅ 成功写入数据到工作表 kgqie6hm!A39:C56
2025-07-18 10:23:17 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:17 - INFO - ✅ 成功设置综合格式: A39:C56
2025-07-18 10:23:17 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:17 - INFO - ✅ 成功合并单元格: B39:C39
2025-07-18 10:23:17 - INFO - 🕐 检测到时间格式，设置为文本格式: 2025/07/18 10:23:17
2025-07-18 10:23:18 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:18 - INFO - ✅ 成功设置合并单元格的值和格式: B39 = '2025/07/18 10:23:17'
2025-07-18 10:23:18 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:18 - INFO - ✅ 成功合并单元格: A40:C40
2025-07-18 10:23:18 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:18 - INFO - ✅ 成功设置合并单元格的值和格式: A40 = '仓库状态数据'
2025-07-18 10:23:18 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:18 - INFO - ✅ 成功合并单元格: B44:C44
2025-07-18 10:23:18 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:19 - INFO - ✅ 成功设置合并单元格的值和格式: B44 = '未完成：5/13，已完成：8/13'
2025-07-18 10:23:19 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:19 - INFO - ✅ 成功合并单元格: B45:C45
2025-07-18 10:23:19 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:19 - INFO - ✅ 成功设置合并单元格的值和格式: B45 = '164'
2025-07-18 10:23:19 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:19 - INFO - ✅ 成功合并单元格: B46:C46
2025-07-18 10:23:19 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:20 - INFO - ✅ 成功设置合并单元格的值和格式: B46 = '2688'
2025-07-18 10:23:20 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:20 - INFO - ✅ 成功合并单元格: A47:C47
2025-07-18 10:23:20 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:20 - INFO - ✅ 成功设置合并单元格的值和格式: A47 = '发货数据'
2025-07-18 10:23:21 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:21 - INFO - ✅ 成功合并单元格: B48:C48
2025-07-18 10:23:21 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:21 - INFO - ✅ 成功设置合并单元格的值和格式: B48 = '2644'
2025-07-18 10:23:21 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:21 - INFO - ✅ 成功合并单元格: B49:C49
2025-07-18 10:23:21 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:21 - INFO - ✅ 成功设置合并单元格的值和格式: B49 = '4652.41kg'
2025-07-18 10:23:22 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:22 - INFO - ✅ 成功合并单元格: B50:C50
2025-07-18 10:23:22 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:22 - INFO - ✅ 成功设置合并单元格的值和格式: B50 = '186'
2025-07-18 10:23:22 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:22 - INFO - ✅ 成功合并单元格: B51:C51
2025-07-18 10:23:22 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:23 - INFO - ✅ 成功设置合并单元格的值和格式: B51 = '3177.37kg'
2025-07-18 10:23:23 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:23 - INFO - ✅ 成功合并单元格: A52:C52
2025-07-18 10:23:23 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:23 - INFO - ✅ 成功设置合并单元格的值和格式: A52 = '奖惩计算'
2025-07-18 10:23:23 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:23 - INFO - ✅ 成功合并单元格: B53:C53
2025-07-18 10:23:23 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:23 - INFO - ✅ 成功设置合并单元格的值和格式: B53 = '奖励 +10元'
2025-07-18 10:23:24 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:24 - INFO - ✅ 成功合并单元格: B54:C54
2025-07-18 10:23:24 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:24 - INFO - ✅ 成功设置合并单元格的值和格式: B54 = '奖励 +25元'
2025-07-18 10:23:24 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:24 - INFO - ✅ 成功合并单元格: B55:C55
2025-07-18 10:23:24 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:24 - INFO - ✅ 成功设置合并单元格的值和格式: B55 = '+35元'
2025-07-18 10:23:25 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:25 - INFO - ✅ 成功合并单元格: A56:C56
2025-07-18 10:23:25 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:25 - INFO - ✅ 成功设置合并单元格的值和格式: A56 = '已审核订单：2,852单 待补货订单：0单 待处理订单：0单 | 待补货奖惩（10元规则）：已审核≤4000且待补货≤15 结果：奖励 10元 | 待处理奖惩（25元规则）：已审核≤4000且待处理≤28（1%） 结果：奖励 25元 | 总计奖惩：+35元'
2025-07-18 10:23:25 - INFO - ℹ️ 钉钉表格API不支持边框设置，跳过边框设置: A39:C56
2025-07-18 10:23:25 - INFO - ✅ 成功追加仓库报告块到第 39-56 行
2025-07-18 10:23:25 - INFO - ✅ 历史数据成功写入钉钉表格！
2025-07-18 10:23:25 - INFO - 📝 已更新测试环境配置文件，记录最后一行: 56
2025-07-18 10:23:25 - INFO - 📊 开始生成月度汇总数据...
2025-07-18 10:23:25 - INFO - 🚀 开始自动运行月度汇总流程...
2025-07-18 10:23:25 - INFO - ✅ 配置文件加载成功: config.json
2025-07-18 10:23:25 - INFO - 🔧 钉钉表格工具初始化 - 测试环境
2025-07-18 10:23:25 - INFO - 📋 表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 10:23:25 - INFO - 📄 工作表1: 仓库绩效明细
2025-07-18 10:23:25 - INFO - 📄 工作表2: 仓库绩效汇总
2025-07-18 10:23:25 - INFO - ✅ 获取钉钉访问令牌成功
2025-07-18 10:23:25 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 10:23:25 - INFO - ✅ 钉钉表格工具初始化完成
2025-07-18 10:23:25 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:25 - INFO - 📋 使用工作表: 仓库绩效明细 (kgqie6hm)
2025-07-18 10:23:25 - INFO - 🔍 开始读取仓库绩效明细数据...
2025-07-18 10:23:26 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:26 - INFO - 📊 读取到 500 行原始数据
2025-07-18 10:23:26 - INFO - ✅ 成功解析 3 条有效数据记录
2025-07-18 10:23:26 - INFO - 📊 读取到 3 条有效数据记录
2025-07-18 10:23:26 - INFO - 📊 开始生成月度汇总...
2025-07-18 10:23:26 - INFO - 📅 生成了 1 个月的汇总数据
2025-07-18 10:23:26 - INFO - 📝 开始写入Sheet2...
2025-07-18 10:23:26 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:26 - INFO - 📋 开始写入仓库绩效汇总: st-3a20f016-86121
2025-07-18 10:23:26 - INFO - 📝 准备写入数据...
2025-07-18 10:23:26 - INFO - 📝 准备 2025年07月数据汇总 数据...
2025-07-18 10:23:26 - INFO - 📊 开始写入 6 行数据到仓库绩效汇总...
2025-07-18 10:23:26 - INFO - 🔍 调试信息:
2025-07-18 10:23:26 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE/sheets/st-3a20f016-86121/ranges/A1:L6?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-07-18 10:23:26 - INFO - 数据: [['2025年07月数据汇总', '', '', '', '', '', '', '', '', '', '', ''], ['日期', '待补货品数', '待补货订单', '待处理品数', '待处理订单', '现货订单', '定制订单', '发货重量', '已审核定制', '已审核现货', '生产进度', '奖惩金额'], ['07-16', '1', '1', '6', '6', '1306', '453', '100875.0', '205', '2730', '未完成：0/9，已完成：9/9', '+35元'], ['07-18', '0', '0', '0', '0', '2644', '186', '7829.78', '164', '2688', '未完成：5/13，已完成：8/13', '+35元'], ['07-18', '0', '0', '0', '0', '2644', '186', '7829.78', '164', '2688', '未完成：5/13，已完成：8/13', '+35元'], ['', '', '', '', '', '', '', '', '', '', '', '']]
2025-07-18 10:23:26 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:26 - INFO - 响应状态码: 200
2025-07-18 10:23:26 - INFO - 响应内容: {"a1Notation":"A1:L6"}
2025-07-18 10:23:26 - INFO - ✅ 成功写入数据到工作表 st-3a20f016-86121!A1:L6
2025-07-18 10:23:26 - INFO - ✅ 数据写入成功
2025-07-18 10:23:26 - INFO - 🎨 应用格式设置...
2025-07-18 10:23:26 - INFO - 📐 设置所有数据居中对齐...
2025-07-18 10:23:26 - INFO - 🔍 格式设置调试:
2025-07-18 10:23:26 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE/sheets/st-3a20f016-86121/ranges/A1:L6?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-07-18 10:23:26 - INFO - 格式数据: {'horizontalAlignments': [['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center']], 'verticalAlignments': [['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle']]}
2025-07-18 10:23:27 - INFO - 响应状态码: 200
2025-07-18 10:23:27 - INFO - 响应内容: {"a1Notation":"A1:L6"}
2025-07-18 10:23:27 - INFO - 🔗 合并标题行: A1:L1
2025-07-18 10:23:27 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:27 - INFO - ✅ 成功合并单元格: A1:L1
2025-07-18 10:23:27 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:23:27 - INFO - ✅ 成功设置合并单元格的值和格式: A1 = '2025年07月数据汇总'
2025-07-18 10:23:27 - INFO - ✅ 成功合并标题: 2025年07月数据汇总
2025-07-18 10:23:27 - INFO - 🎉 月度汇总数据已成功写入Sheet2！
2025-07-18 10:23:27 - INFO - ✅ 月度汇总数据生成完成
2025-07-18 10:23:27 - INFO - 🎉 日期 2025-07-17 的完整流程处理完成！
2025-07-18 10:23:27 - INFO - ✅ 日期 2025-07-17 的完整流程重新执行成功！
2025-07-18 10:25:26 - INFO - 日志记录已初始化，stdout和stderr现已重定向到日志文件。
2025-07-18 10:25:26 - INFO - 🏭 仓库发货数据钉钉通知程序
2025-07-18 10:25:26 - INFO - ========================================
2025-07-18 10:25:26 - INFO - 🔄 重新执行模式：处理日期 2025-07-14 的完整流程
2025-07-18 10:25:26 - INFO - 📅 目标时间设定为: 2025-07-14 23:00:00
2025-07-18 10:25:26 - INFO - ⚠️  将按23点后的完整流程执行（群通知+个人通知+表格写入）
2025-07-18 10:25:26 - INFO - 🔑 正在获取钉钉access_token...
2025-07-18 10:25:26 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:25:26 - INFO - ✅ 获取access_token成功，有效期: 7200秒
2025-07-18 10:25:26 - INFO - 🔧 钉钉表格工具初始化 - 测试环境
2025-07-18 10:25:26 - INFO - 📋 表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 10:25:26 - INFO - 📄 工作表1: 仓库绩效明细
2025-07-18 10:25:26 - INFO - 📄 工作表2: 仓库绩效汇总
2025-07-18 10:25:27 - INFO - ✅ 获取钉钉访问令牌成功
2025-07-18 10:25:27 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 10:25:27 - INFO - ✅ Excel处理器初始化完成. 数据目录: D:/仓库发货数据
2025-07-18 10:25:27 - INFO - 🏭 开始处理日期为 2025-07-14 的数据（强制完整流程）...
2025-07-18 10:25:27 - INFO - 📊 开始处理最新的仓库状态数据文件...
2025-07-18 10:25:27 - INFO - ✅ 找到最新的 '各部门绩效数据' 文件: 各部门绩效数据.xlsx
2025-07-18 10:25:27 - INFO - - 成功读取 '各部门绩效数据.xlsx' 的 '义乌仓' sheet页
2025-07-18 10:25:27 - ERROR - E:\RPA项目\仓库发货数据拉取\excel_processor.py:361: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
  df[time_col] = pd.to_datetime(df[time_col], errors='coerce')
2025-07-18 10:25:27 - INFO - - 🔄 发现 24 个无法解析的日期，尝试中文格式解析...
2025-07-18 10:25:27 - INFO - - 🔄 重新读取原始Excel文件以获取未转换的日期数据...
2025-07-18 10:25:27 - INFO - - 📋 原始数据类型: <class 'str'>
2025-07-18 10:25:27 - INFO - - 📋 无法解析的日期样本: ['2025年06月27日 09:58:50', '2025年06月27日 10:14:11', '2025年06月28日 09:39:58']
2025-07-18 10:25:27 - INFO - - ✅ 成功解析 24 个中文日期格式
2025-07-18 10:25:27 - INFO - - ℹ️ 在Excel中未找到指定日期 (2025-07-14) 的任何记录。
2025-07-18 10:25:27 - INFO - ❌ 未能从Excel中获取日期为 2025-07-14 的仓库状态数据，跳过处理。
2025-07-18 10:25:27 - INFO - ❌ 日期 2025-07-14 的流程执行失败，请检查数据是否存在。
2025-07-18 10:32:13 - INFO - 日志记录已初始化，stdout和stderr现已重定向到日志文件。
2025-07-18 10:32:13 - INFO - 🏭 仓库发货数据钉钉通知程序
2025-07-18 10:32:13 - INFO - ========================================
2025-07-18 10:32:13 - INFO - 🔄 重新执行模式：处理日期 2025-07-14 的完整流程
2025-07-18 10:32:13 - INFO - 📅 目标时间设定为: 2025-07-14 23:00:00
2025-07-18 10:32:13 - INFO - ⚠️  将按23点后的完整流程执行（群通知+个人通知+表格写入）
2025-07-18 10:32:13 - INFO - 🔑 正在获取钉钉access_token...
2025-07-18 10:32:13 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:13 - INFO - ✅ 获取access_token成功，有效期: 7200秒
2025-07-18 10:32:13 - INFO - 🔧 钉钉表格工具初始化 - 测试环境
2025-07-18 10:32:13 - INFO - 📋 表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 10:32:13 - INFO - 📄 工作表1: 仓库绩效明细
2025-07-18 10:32:13 - INFO - 📄 工作表2: 仓库绩效汇总
2025-07-18 10:32:13 - INFO - ✅ 获取钉钉访问令牌成功
2025-07-18 10:32:13 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 10:32:13 - INFO - ✅ Excel处理器初始化完成. 数据目录: D:/仓库发货数据
2025-07-18 10:32:13 - INFO - 🏭 开始处理日期为 2025-07-14 的数据（强制完整流程）...
2025-07-18 10:32:13 - INFO - 📊 开始处理最新的仓库状态数据文件...
2025-07-18 10:32:13 - INFO - ✅ 找到最新的 '各部门绩效数据' 文件: 各部门绩效数据.xlsx
2025-07-18 10:32:13 - INFO - - 成功读取 '各部门绩效数据.xlsx' 的 '义乌仓' sheet页
2025-07-18 10:32:13 - ERROR - E:\RPA项目\仓库发货数据拉取\excel_processor.py:361: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
  df[time_col] = pd.to_datetime(df[time_col], errors='coerce')
2025-07-18 10:32:13 - INFO - - 🔄 发现 43 个无法解析的日期，尝试中文格式解析...
2025-07-18 10:32:13 - INFO - - 🔄 重新读取原始Excel文件以获取未转换的日期数据...
2025-07-18 10:32:13 - INFO - - 📋 原始数据类型: <class 'str'>
2025-07-18 10:32:13 - INFO - - 📋 无法解析的日期样本: ['2025年06月27日 09:58:50', '2025年06月27日 10:14:11', '2025年06月28日 09:39:58']
2025-07-18 10:32:13 - INFO - - ✅ 成功解析 43 个中文日期格式
2025-07-18 10:32:13 - INFO - - ✅ 找到匹配的数据行，开始提取...
2025-07-18 10:32:13 - INFO - - 提取到数据，报告日期为: 2025/07/14, 数据时间戳: 2025-07-14 23:06:33
2025-07-18 10:32:13 - INFO - 📊 开始处理待处理订单文件...
2025-07-18 10:32:13 - INFO - ⚠️ 在目录 'D:/仓库发货数据' 中未找到包含'待处理订单'关键词的Excel文件
2025-07-18 10:32:13 - INFO - 🕚 强制按23点后完整流程执行：群通知 + 个人通知 + 表格写入
2025-07-18 10:32:13 - INFO - 📤 正在发送主报告到群...
2025-07-18 10:32:14 - INFO - ✅ 钉钉消息发送成功
2025-07-18 10:32:14 - INFO - ✅ 主报告发送成功！
2025-07-18 10:32:14 - INFO - 📊 奖惩计算基础数据：
2025-07-18 10:32:14 - INFO - 已审核-定制：208单
2025-07-18 10:32:14 - INFO - 已审核-现货：2803单
2025-07-18 10:32:14 - INFO - 已审核订单总计：3011单 (定制+现货)
2025-07-18 10:32:14 - INFO - 待补货订单：0单
2025-07-18 10:32:14 - INFO - 待处理订单：0单
2025-07-18 10:32:15 - INFO - 📤 正在发送奖惩报告到个人...
2025-07-18 10:32:15 - INFO - 📤 当前环境: 测试环境
2025-07-18 10:32:15 - INFO - 📤 发送目标: 杜陶祎（信息部） (userId: 01415968542326653172)
2025-07-18 10:32:15 - INFO - 📤 使用机器人发送个人消息给用户 01415968542326653172
2025-07-18 10:32:15 - INFO - 📋 消息标题: 奖惩计算结果
2025-07-18 10:32:15 - INFO - 📄 消息内容: 奖惩计算结果

基础数据：

已审核订单：3,011单

待补货订单：0单

待处理订单：0单

待补货奖惩（10元规则）：
条件：已审核≤4000且待补货≤15
结果：奖励 +10元

待处理奖惩（...
2025-07-18 10:32:15 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:15 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:15 - INFO - 📊 发送结果: {'flowControlledStaffIdList': [], 'invalidStaffIdList': [], 'processQueryKey': 'yA2wntR1KyW1GHxaZST6/II7xE4QEG+eIvAQuqZqjos='}
2025-07-18 10:32:15 - INFO - ✅ 机器人消息发送成功
2025-07-18 10:32:15 - INFO - 任务查询键: yA2wntR1KyW1GHxaZST6/II7xE4QEG+eIvAQuqZqjos=
2025-07-18 10:32:15 - INFO - ✅ 奖惩报告已发送给杜陶祎（信息部） - 测试环境
2025-07-18 10:32:15 - INFO - 📊 正在将报告块写入到钉钉表格...
2025-07-18 10:32:15 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:15 - INFO - 📝 追加日期为 2025/07/14 的历史数据到表格...
2025-07-18 10:32:15 - INFO - 🔍 配置文件无记录，开始全表扫描最后一行...
2025-07-18 10:32:15 - INFO - 🔍 正在高效查找最后一行数据（一次性读取最多 1000 行）...
2025-07-18 10:32:15 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:16 - INFO - 📍 找到最后数据行: 56
2025-07-18 10:32:16 - INFO - 📍 找到最后数据行: 56，下一个数据块将从第 58 行开始（留出1行空行分隔）
2025-07-18 10:32:16 - INFO - 🔗 正在合并分隔空行: A57:C57
2025-07-18 10:32:16 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:16 - INFO - ✅ 成功合并单元格: A57:C57
2025-07-18 10:32:16 - INFO - 🔍 调试信息:
2025-07-18 10:32:16 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE/sheets/kgqie6hm/ranges/A58:C75?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-07-18 10:32:16 - INFO - 数据: [['数据统计时间', '2025/07/18 10:32:16', ''], ['仓库状态数据', '', ''], ['类型', '品数', '订单数'], ['待补货', '0', '0'], ['待处理', '0', '0'], ['生产单完成进度', '', ''], ['已审核-定制', '', ''], ['已审核-现货', '', ''], ['发货数据', '', ''], ['现货订单数', '', ''], ['现货订单发货总重量', '', ''], ['定制订单数', '', ''], ['定制订单发货总重量', '', ''], ['奖惩计算', '', ''], ['待补货奖惩', '', ''], ['待处理奖惩', '', ''], ['总计奖惩', '', ''], ['奖惩计算结果', '', '']]
2025-07-18 10:32:16 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:17 - INFO - 响应状态码: 200
2025-07-18 10:32:17 - INFO - 响应内容: {"a1Notation":"A58:C75"}
2025-07-18 10:32:17 - INFO - ✅ 成功写入数据到工作表 kgqie6hm!A58:C75
2025-07-18 10:32:17 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:17 - INFO - ✅ 成功设置综合格式: A58:C75
2025-07-18 10:32:17 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:17 - INFO - ✅ 成功合并单元格: B58:C58
2025-07-18 10:32:17 - INFO - 🕐 检测到时间格式，设置为文本格式: 2025/07/18 10:32:16
2025-07-18 10:32:17 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:17 - INFO - ✅ 成功设置合并单元格的值和格式: B58 = '2025/07/18 10:32:16'
2025-07-18 10:32:17 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:18 - INFO - ✅ 成功合并单元格: A59:C59
2025-07-18 10:32:18 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:18 - INFO - ✅ 成功设置合并单元格的值和格式: A59 = '仓库状态数据'
2025-07-18 10:32:18 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:18 - INFO - ✅ 成功合并单元格: B63:C63
2025-07-18 10:32:18 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:18 - INFO - ✅ 成功设置合并单元格的值和格式: B63 = '未完成：3/8，已完成：5/8'
2025-07-18 10:32:18 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:18 - INFO - ✅ 成功合并单元格: B64:C64
2025-07-18 10:32:18 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:18 - INFO - ✅ 成功设置合并单元格的值和格式: B64 = '208'
2025-07-18 10:32:19 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:19 - INFO - ✅ 成功合并单元格: B65:C65
2025-07-18 10:32:19 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:19 - INFO - ✅ 成功设置合并单元格的值和格式: B65 = '2803'
2025-07-18 10:32:19 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:19 - INFO - ✅ 成功合并单元格: A66:C66
2025-07-18 10:32:19 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:19 - INFO - ✅ 成功设置合并单元格的值和格式: A66 = '发货数据'
2025-07-18 10:32:19 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:19 - INFO - ✅ 成功合并单元格: B67:C67
2025-07-18 10:32:19 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:20 - INFO - ✅ 成功设置合并单元格的值和格式: B67 = '0'
2025-07-18 10:32:20 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:20 - INFO - ✅ 成功合并单元格: B68:C68
2025-07-18 10:32:20 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:20 - INFO - ✅ 成功设置合并单元格的值和格式: B68 = '0.0kg'
2025-07-18 10:32:20 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:20 - INFO - ✅ 成功合并单元格: B69:C69
2025-07-18 10:32:20 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:20 - INFO - ✅ 成功设置合并单元格的值和格式: B69 = '0'
2025-07-18 10:32:20 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:21 - INFO - ✅ 成功合并单元格: B70:C70
2025-07-18 10:32:21 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:21 - INFO - ✅ 成功设置合并单元格的值和格式: B70 = '0.0kg'
2025-07-18 10:32:21 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:21 - INFO - ✅ 成功合并单元格: A71:C71
2025-07-18 10:32:21 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:21 - INFO - ✅ 成功设置合并单元格的值和格式: A71 = '奖惩计算'
2025-07-18 10:32:21 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:21 - INFO - ✅ 成功合并单元格: B72:C72
2025-07-18 10:32:21 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:21 - INFO - ✅ 成功设置合并单元格的值和格式: B72 = '奖励 +10元'
2025-07-18 10:32:22 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:22 - INFO - ✅ 成功合并单元格: B73:C73
2025-07-18 10:32:22 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:22 - INFO - ✅ 成功设置合并单元格的值和格式: B73 = '奖励 +25元'
2025-07-18 10:32:22 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:22 - INFO - ✅ 成功合并单元格: B74:C74
2025-07-18 10:32:22 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:22 - INFO - ✅ 成功设置合并单元格的值和格式: B74 = '+35元'
2025-07-18 10:32:22 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:22 - INFO - ✅ 成功合并单元格: A75:C75
2025-07-18 10:32:23 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:23 - INFO - ✅ 成功设置合并单元格的值和格式: A75 = '已审核订单：3,011单 待补货订单：0单 待处理订单：0单 | 待补货奖惩（10元规则）：已审核≤4000且待补货≤15 结果：奖励 10元 | 待处理奖惩（25元规则）：已审核≤4000且待处理≤30（1%） 结果：奖励 25元 | 总计奖惩：+35元'
2025-07-18 10:32:23 - INFO - ℹ️ 钉钉表格API不支持边框设置，跳过边框设置: A58:C75
2025-07-18 10:32:23 - INFO - ✅ 成功追加仓库报告块到第 58-75 行
2025-07-18 10:32:23 - INFO - ✅ 历史数据成功写入钉钉表格！
2025-07-18 10:32:23 - INFO - 📝 已更新测试环境配置文件，记录最后一行: 75
2025-07-18 10:32:23 - INFO - 📊 开始生成月度汇总数据...
2025-07-18 10:32:23 - INFO - 🚀 开始自动运行月度汇总流程...
2025-07-18 10:32:23 - INFO - ✅ 配置文件加载成功: config.json
2025-07-18 10:32:23 - INFO - 🔧 钉钉表格工具初始化 - 测试环境
2025-07-18 10:32:23 - INFO - 📋 表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 10:32:23 - INFO - 📄 工作表1: 仓库绩效明细
2025-07-18 10:32:23 - INFO - 📄 工作表2: 仓库绩效汇总
2025-07-18 10:32:23 - INFO - ✅ 获取钉钉访问令牌成功
2025-07-18 10:32:23 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 10:32:23 - INFO - ✅ 钉钉表格工具初始化完成
2025-07-18 10:32:23 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:23 - INFO - 📋 使用工作表: 仓库绩效明细 (kgqie6hm)
2025-07-18 10:32:23 - INFO - 🔍 开始读取仓库绩效明细数据...
2025-07-18 10:32:23 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:24 - INFO - 📊 读取到 500 行原始数据
2025-07-18 10:32:24 - INFO - ✅ 成功解析 4 条有效数据记录
2025-07-18 10:32:24 - INFO - 📊 读取到 4 条有效数据记录
2025-07-18 10:32:24 - INFO - 📊 开始生成月度汇总...
2025-07-18 10:32:24 - INFO - 📅 生成了 1 个月的汇总数据
2025-07-18 10:32:24 - INFO - 📝 开始写入Sheet2...
2025-07-18 10:32:24 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:24 - INFO - 📋 开始写入仓库绩效汇总: st-3a20f016-86121
2025-07-18 10:32:24 - INFO - 📝 准备写入数据...
2025-07-18 10:32:24 - INFO - 📝 准备 2025年07月数据汇总 数据...
2025-07-18 10:32:24 - INFO - 📊 开始写入 7 行数据到仓库绩效汇总...
2025-07-18 10:32:24 - INFO - 🔍 调试信息:
2025-07-18 10:32:24 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE/sheets/st-3a20f016-86121/ranges/A1:L7?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-07-18 10:32:24 - INFO - 数据: [['2025年07月数据汇总', '', '', '', '', '', '', '', '', '', '', ''], ['日期', '待补货品数', '待补货订单', '待处理品数', '待处理订单', '现货订单', '定制订单', '发货重量', '已审核定制', '已审核现货', '生产进度', '奖惩金额'], ['07-16', '1', '1', '6', '6', '1306', '453', '100875.0', '205', '2730', '未完成：0/9，已完成：9/9', '+35元'], ['07-18', '0', '0', '0', '0', '2644', '186', '7829.78', '164', '2688', '未完成：5/13，已完成：8/13', '+35元'], ['07-18', '0', '0', '0', '0', '2644', '186', '7829.78', '164', '2688', '未完成：5/13，已完成：8/13', '+35元'], ['07-18', '0', '0', '0', '0', '0', '0', '0.0', '208', '2803', '未完成：3/8，已完成：5/8', '+35元'], ['', '', '', '', '', '', '', '', '', '', '', '']]
2025-07-18 10:32:24 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:24 - INFO - 响应状态码: 200
2025-07-18 10:32:24 - INFO - 响应内容: {"a1Notation":"A1:L7"}
2025-07-18 10:32:24 - INFO - ✅ 成功写入数据到工作表 st-3a20f016-86121!A1:L7
2025-07-18 10:32:24 - INFO - ✅ 数据写入成功
2025-07-18 10:32:24 - INFO - 🎨 应用格式设置...
2025-07-18 10:32:24 - INFO - 📐 设置所有数据居中对齐...
2025-07-18 10:32:24 - INFO - 🔍 格式设置调试:
2025-07-18 10:32:24 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE/sheets/st-3a20f016-86121/ranges/A1:L7?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-07-18 10:32:24 - INFO - 格式数据: {'horizontalAlignments': [['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center']], 'verticalAlignments': [['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle']]}
2025-07-18 10:32:24 - INFO - 响应状态码: 200
2025-07-18 10:32:24 - INFO - 响应内容: {"a1Notation":"A1:L7"}
2025-07-18 10:32:24 - INFO - 🔗 合并标题行: A1:L1
2025-07-18 10:32:24 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:25 - INFO - ✅ 成功合并单元格: A1:L1
2025-07-18 10:32:25 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 10:32:25 - INFO - ✅ 成功设置合并单元格的值和格式: A1 = '2025年07月数据汇总'
2025-07-18 10:32:25 - INFO - ✅ 成功合并标题: 2025年07月数据汇总
2025-07-18 10:32:25 - INFO - 🎉 月度汇总数据已成功写入Sheet2！
2025-07-18 10:32:25 - INFO - ✅ 月度汇总数据生成完成
2025-07-18 10:32:25 - INFO - 🎉 日期 2025-07-14 的完整流程处理完成！
2025-07-18 10:32:25 - INFO - ✅ 日期 2025-07-14 的完整流程重新执行成功！
2025-07-18 11:00:49 - INFO - 日志记录已初始化，stdout和stderr现已重定向到日志文件。
2025-07-18 11:00:49 - INFO - 🏭 仓库发货数据钉钉通知程序
2025-07-18 11:00:49 - INFO - ========================================
2025-07-18 11:00:49 - INFO - 🔄 重新执行模式：处理日期 2025-07-14 的完整流程
2025-07-18 11:00:49 - INFO - 📅 目标时间设定为: 2025-07-14 23:00:00
2025-07-18 11:00:49 - INFO - ⚠️  将按23点后的完整流程执行（群通知+个人通知+表格写入）
2025-07-18 11:00:49 - INFO - 🔑 正在获取钉钉access_token...
2025-07-18 11:00:49 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:49 - INFO - ✅ 获取access_token成功，有效期: 7200秒
2025-07-18 11:00:49 - INFO - 🔧 钉钉表格工具初始化 - 测试环境
2025-07-18 11:00:49 - INFO - 📋 表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 11:00:49 - INFO - 📄 工作表1: 仓库绩效明细
2025-07-18 11:00:49 - INFO - 📄 工作表2: 仓库绩效汇总
2025-07-18 11:00:49 - INFO - ✅ 获取钉钉访问令牌成功
2025-07-18 11:00:49 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 11:00:49 - INFO - ✅ Excel处理器初始化完成. 数据目录: D:/仓库发货数据
2025-07-18 11:00:49 - INFO - 🏭 开始处理日期为 2025-07-14 的数据（强制完整流程）...
2025-07-18 11:00:49 - INFO - 📊 开始处理最新的仓库状态数据文件...
2025-07-18 11:00:49 - INFO - ✅ 找到最新的 '各部门绩效数据' 文件: 各部门绩效数据.xlsx
2025-07-18 11:00:49 - INFO - - 成功读取 '各部门绩效数据.xlsx' 的 '义乌仓' sheet页
2025-07-18 11:00:49 - ERROR - E:\RPA项目\仓库发货数据拉取\excel_processor.py:361: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
  df[time_col] = pd.to_datetime(df[time_col], errors='coerce')
2025-07-18 11:00:49 - INFO - - 🔄 发现 43 个无法解析的日期，尝试中文格式解析...
2025-07-18 11:00:49 - INFO - - 🔄 重新读取原始Excel文件以获取未转换的日期数据...
2025-07-18 11:00:49 - INFO - - 📋 原始数据类型: <class 'str'>
2025-07-18 11:00:49 - INFO - - 📋 无法解析的日期样本: ['2025年06月27日 09:58:50', '2025年06月27日 10:14:11', '2025年06月28日 09:39:58']
2025-07-18 11:00:49 - INFO - - ✅ 成功解析 43 个中文日期格式
2025-07-18 11:00:49 - INFO - - ✅ 找到匹配的数据行，开始提取...
2025-07-18 11:00:49 - INFO - - 提取到数据，报告日期为: 2025/07/14, 数据时间戳: 2025-07-14 23:06:33
2025-07-18 11:00:49 - INFO - 📊 开始处理待处理订单文件...
2025-07-18 11:00:49 - INFO - ⚠️ 在目录 'D:/仓库发货数据' 中未找到包含'待处理订单'关键词的Excel文件
2025-07-18 11:00:49 - INFO - 🕚 强制按23点后完整流程执行：群通知 + 个人通知 + 表格写入
2025-07-18 11:00:49 - INFO - 📤 正在发送主报告到群...
2025-07-18 11:00:49 - INFO - ✅ 钉钉消息发送成功
2025-07-18 11:00:49 - INFO - ✅ 主报告发送成功！
2025-07-18 11:00:49 - INFO - 📊 奖惩计算基础数据：
2025-07-18 11:00:49 - INFO - 已审核-定制：208单
2025-07-18 11:00:49 - INFO - 已审核-现货：2803单
2025-07-18 11:00:49 - INFO - 已审核订单总计：3011单 (定制+现货)
2025-07-18 11:00:49 - INFO - 待补货订单：7单
2025-07-18 11:00:49 - INFO - 待处理订单：6单
2025-07-18 11:00:50 - INFO - 📤 正在发送奖惩报告到个人...
2025-07-18 11:00:50 - INFO - 📤 当前环境: 测试环境
2025-07-18 11:00:50 - INFO - 📤 发送目标: 杜陶祎（信息部） (userId: 01415968542326653172)
2025-07-18 11:00:50 - INFO - 📤 使用机器人发送个人消息给用户 01415968542326653172
2025-07-18 11:00:50 - INFO - 📋 消息标题: 奖惩计算结果
2025-07-18 11:00:50 - INFO - 📄 消息内容: 奖惩计算结果

基础数据：

已审核订单：3,011单

待补货订单：7单

待处理订单：6单

待补货奖惩（10元规则）：
条件：已审核≤4000且待补货≤15
结果：奖励 +10元

待处理奖惩（...
2025-07-18 11:00:51 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:51 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:51 - INFO - 📊 发送结果: {'flowControlledStaffIdList': [], 'invalidStaffIdList': [], 'processQueryKey': 'T7aWJw2dfX4Hm+CUMfk6kwPVdf2+xjL6iGteqhnZciA='}
2025-07-18 11:00:51 - INFO - ✅ 机器人消息发送成功
2025-07-18 11:00:51 - INFO - 任务查询键: T7aWJw2dfX4Hm+CUMfk6kwPVdf2+xjL6iGteqhnZciA=
2025-07-18 11:00:51 - INFO - ✅ 奖惩报告已发送给杜陶祎（信息部） - 测试环境
2025-07-18 11:00:51 - INFO - 📊 正在将报告块写入到钉钉表格...
2025-07-18 11:00:51 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:51 - INFO - 📝 追加日期为 2025/07/14 的历史数据到表格...
2025-07-18 11:00:51 - INFO - 🔍 配置文件无记录，开始全表扫描最后一行...
2025-07-18 11:00:51 - INFO - 🔍 正在高效查找最后一行数据（一次性读取最多 1000 行）...
2025-07-18 11:00:51 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:52 - INFO - 📍 找到最后数据行: 75
2025-07-18 11:00:52 - INFO - 📍 找到最后数据行: 75，下一个数据块将从第 77 行开始（留出1行空行分隔）
2025-07-18 11:00:52 - INFO - 🔗 正在合并分隔空行: A76:C76
2025-07-18 11:00:52 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:52 - INFO - ✅ 成功合并单元格: A76:C76
2025-07-18 11:00:52 - INFO - 🔍 调试信息:
2025-07-18 11:00:52 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE/sheets/kgqie6hm/ranges/A77:C94?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-07-18 11:00:52 - INFO - 数据: [['数据统计时间', '2025/07/18 11:00:52', ''], ['仓库状态数据', '', ''], ['类型', '品数', '订单数'], ['待补货', '9', '7'], ['待处理', '13', '6'], ['生产单完成进度', '', ''], ['已审核-定制', '', ''], ['已审核-现货', '', ''], ['发货数据', '', ''], ['现货订单数', '', ''], ['现货订单发货总重量', '', ''], ['定制订单数', '', ''], ['定制订单发货总重量', '', ''], ['奖惩计算', '', ''], ['待补货奖惩', '', ''], ['待处理奖惩', '', ''], ['总计奖惩', '', ''], ['奖惩计算结果', '', '']]
2025-07-18 11:00:52 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:53 - INFO - 响应状态码: 200
2025-07-18 11:00:53 - INFO - 响应内容: {"a1Notation":"A77:C94"}
2025-07-18 11:00:53 - INFO - ✅ 成功写入数据到工作表 kgqie6hm!A77:C94
2025-07-18 11:00:53 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:53 - INFO - ✅ 成功设置综合格式: A77:C94
2025-07-18 11:00:53 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:53 - INFO - ✅ 成功合并单元格: B77:C77
2025-07-18 11:00:53 - INFO - 🕐 检测到时间格式，设置为文本格式: 2025/07/18 11:00:52
2025-07-18 11:00:53 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:53 - INFO - ✅ 成功设置合并单元格的值和格式: B77 = '2025/07/18 11:00:52'
2025-07-18 11:00:53 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:53 - INFO - ✅ 成功合并单元格: A78:C78
2025-07-18 11:00:53 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:54 - INFO - ✅ 成功设置合并单元格的值和格式: A78 = '仓库状态数据'
2025-07-18 11:00:54 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:54 - INFO - ✅ 成功合并单元格: B82:C82
2025-07-18 11:00:54 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:54 - INFO - ✅ 成功设置合并单元格的值和格式: B82 = '未完成：3/8，已完成：5/8'
2025-07-18 11:00:54 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:54 - INFO - ✅ 成功合并单元格: B83:C83
2025-07-18 11:00:54 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:54 - INFO - ✅ 成功设置合并单元格的值和格式: B83 = '208'
2025-07-18 11:00:54 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:54 - INFO - ✅ 成功合并单元格: B84:C84
2025-07-18 11:00:55 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:55 - INFO - ✅ 成功设置合并单元格的值和格式: B84 = '2803'
2025-07-18 11:00:55 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:55 - INFO - ✅ 成功合并单元格: A85:C85
2025-07-18 11:00:55 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:55 - INFO - ✅ 成功设置合并单元格的值和格式: A85 = '发货数据'
2025-07-18 11:00:55 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:55 - INFO - ✅ 成功合并单元格: B86:C86
2025-07-18 11:00:55 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:55 - INFO - ✅ 成功设置合并单元格的值和格式: B86 = '0'
2025-07-18 11:00:56 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:56 - INFO - ✅ 成功合并单元格: B87:C87
2025-07-18 11:00:56 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:56 - INFO - ✅ 成功设置合并单元格的值和格式: B87 = '0.0kg'
2025-07-18 11:00:56 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:56 - INFO - ✅ 成功合并单元格: B88:C88
2025-07-18 11:00:56 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:56 - INFO - ✅ 成功设置合并单元格的值和格式: B88 = '0'
2025-07-18 11:00:56 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:57 - INFO - ✅ 成功合并单元格: B89:C89
2025-07-18 11:00:57 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:57 - INFO - ✅ 成功设置合并单元格的值和格式: B89 = '0.0kg'
2025-07-18 11:00:57 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:57 - INFO - ✅ 成功合并单元格: A90:C90
2025-07-18 11:00:57 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:57 - INFO - ✅ 成功设置合并单元格的值和格式: A90 = '奖惩计算'
2025-07-18 11:00:57 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:57 - INFO - ✅ 成功合并单元格: B91:C91
2025-07-18 11:00:57 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:58 - INFO - ✅ 成功设置合并单元格的值和格式: B91 = '奖励 +10元'
2025-07-18 11:00:58 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:58 - INFO - ✅ 成功合并单元格: B92:C92
2025-07-18 11:00:58 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:58 - INFO - ✅ 成功设置合并单元格的值和格式: B92 = '奖励 +25元'
2025-07-18 11:00:58 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:58 - INFO - ✅ 成功合并单元格: B93:C93
2025-07-18 11:00:58 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:58 - INFO - ✅ 成功设置合并单元格的值和格式: B93 = '+35元'
2025-07-18 11:00:59 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:59 - INFO - ✅ 成功合并单元格: A94:C94
2025-07-18 11:00:59 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:59 - INFO - ✅ 成功设置合并单元格的值和格式: A94 = '已审核订单：3,011单 待补货订单：7单 待处理订单：6单 | 待补货奖惩（10元规则）：已审核≤4000且待补货≤15 结果：奖励 10元 | 待处理奖惩（25元规则）：已审核≤4000且待处理≤30（1%） 结果：奖励 25元 | 总计奖惩：+35元'
2025-07-18 11:00:59 - INFO - ℹ️ 钉钉表格API不支持边框设置，跳过边框设置: A77:C94
2025-07-18 11:00:59 - INFO - ✅ 成功追加仓库报告块到第 77-94 行
2025-07-18 11:00:59 - INFO - ✅ 历史数据成功写入钉钉表格！
2025-07-18 11:00:59 - INFO - 📝 已更新测试环境配置文件，记录最后一行: 94
2025-07-18 11:00:59 - INFO - 📊 开始生成月度汇总数据...
2025-07-18 11:00:59 - INFO - 🚀 开始自动运行月度汇总流程...
2025-07-18 11:00:59 - INFO - ✅ 配置文件加载成功: config.json
2025-07-18 11:00:59 - INFO - 🔧 钉钉表格工具初始化 - 测试环境
2025-07-18 11:00:59 - INFO - 📋 表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 11:00:59 - INFO - 📄 工作表1: 仓库绩效明细
2025-07-18 11:00:59 - INFO - 📄 工作表2: 仓库绩效汇总
2025-07-18 11:00:59 - INFO - ✅ 获取钉钉访问令牌成功
2025-07-18 11:00:59 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 11:00:59 - INFO - ✅ 钉钉表格工具初始化完成
2025-07-18 11:00:59 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:00:59 - INFO - 📋 使用工作表: 仓库绩效明细 (kgqie6hm)
2025-07-18 11:00:59 - INFO - 🔍 开始读取仓库绩效明细数据...
2025-07-18 11:00:59 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:01:00 - INFO - 📊 读取到 500 行原始数据
2025-07-18 11:01:00 - INFO - ✅ 成功解析 5 条有效数据记录
2025-07-18 11:01:00 - INFO - 📊 读取到 5 条有效数据记录
2025-07-18 11:01:00 - INFO - 📊 开始生成月度汇总...
2025-07-18 11:01:00 - INFO - 📅 生成了 1 个月的汇总数据
2025-07-18 11:01:00 - INFO - 📝 开始写入Sheet2...
2025-07-18 11:01:00 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:01:00 - INFO - 📋 开始写入仓库绩效汇总: st-3a20f016-86121
2025-07-18 11:01:00 - INFO - 📝 准备写入数据...
2025-07-18 11:01:00 - INFO - 📝 准备 2025年07月数据汇总 数据...
2025-07-18 11:01:00 - INFO - 📊 开始写入 8 行数据到仓库绩效汇总...
2025-07-18 11:01:00 - INFO - 🔍 调试信息:
2025-07-18 11:01:00 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE/sheets/st-3a20f016-86121/ranges/A1:L8?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-07-18 11:01:00 - INFO - 数据: [['2025年07月数据汇总', '', '', '', '', '', '', '', '', '', '', ''], ['日期', '待补货品数', '待补货订单', '待处理品数', '待处理订单', '现货订单', '定制订单', '发货重量', '已审核定制', '已审核现货', '生产进度', '奖惩金额'], ['07-16', '1', '1', '6', '6', '1306', '453', '100875.0', '205', '2730', '未完成：0/9，已完成：9/9', '+35元'], ['07-18', '0', '0', '0', '0', '2644', '186', '7829.78', '164', '2688', '未完成：5/13，已完成：8/13', '+35元'], ['07-18', '0', '0', '0', '0', '2644', '186', '7829.78', '164', '2688', '未完成：5/13，已完成：8/13', '+35元'], ['07-18', '0', '0', '0', '0', '0', '0', '0.0', '208', '2803', '未完成：3/8，已完成：5/8', '+35元'], ['07-18', '9', '7', '13', '6', '0', '0', '0.0', '208', '2803', '未完成：3/8，已完成：5/8', '+35元'], ['', '', '', '', '', '', '', '', '', '', '', '']]
2025-07-18 11:01:00 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:01:00 - INFO - 响应状态码: 200
2025-07-18 11:01:00 - INFO - 响应内容: {"a1Notation":"A1:L8"}
2025-07-18 11:01:00 - INFO - ✅ 成功写入数据到工作表 st-3a20f016-86121!A1:L8
2025-07-18 11:01:00 - INFO - ✅ 数据写入成功
2025-07-18 11:01:00 - INFO - 🎨 应用格式设置...
2025-07-18 11:01:00 - INFO - 📐 设置所有数据居中对齐...
2025-07-18 11:01:00 - INFO - 🔍 格式设置调试:
2025-07-18 11:01:00 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE/sheets/st-3a20f016-86121/ranges/A1:L8?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-07-18 11:01:00 - INFO - 格式数据: {'horizontalAlignments': [['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center']], 'verticalAlignments': [['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle']]}
2025-07-18 11:01:01 - INFO - 响应状态码: 200
2025-07-18 11:01:01 - INFO - 响应内容: {"a1Notation":"A1:L8"}
2025-07-18 11:01:01 - INFO - 🔗 合并标题行: A1:L1
2025-07-18 11:01:01 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:01:01 - INFO - ✅ 成功合并单元格: A1:L1
2025-07-18 11:01:01 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:01:01 - INFO - ✅ 成功设置合并单元格的值和格式: A1 = '2025年07月数据汇总'
2025-07-18 11:01:01 - INFO - ✅ 成功合并标题: 2025年07月数据汇总
2025-07-18 11:01:01 - INFO - 🎉 月度汇总数据已成功写入Sheet2！
2025-07-18 11:01:01 - INFO - ✅ 月度汇总数据生成完成
2025-07-18 11:01:01 - INFO - 🎉 日期 2025-07-14 的完整流程处理完成！
2025-07-18 11:01:01 - INFO - ✅ 日期 2025-07-14 的完整流程重新执行成功！
2025-07-18 11:07:58 - INFO - 日志记录已初始化，stdout和stderr现已重定向到日志文件。
2025-07-18 11:07:58 - INFO - 🏭 仓库发货数据钉钉通知程序
2025-07-18 11:07:58 - INFO - ========================================
2025-07-18 11:07:58 - INFO - 🔄 重新执行模式：处理日期 2025-07-14 的完整流程
2025-07-18 11:07:58 - INFO - 📅 目标时间设定为: 2025-07-14 23:00:00
2025-07-18 11:07:58 - INFO - ⚠️  将按23点后的完整流程执行（群通知+个人通知+表格写入）
2025-07-18 11:07:58 - INFO - 🔑 正在获取钉钉access_token...
2025-07-18 11:07:58 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:07:58 - INFO - ✅ 获取access_token成功，有效期: 7200秒
2025-07-18 11:07:58 - INFO - 🔧 钉钉表格工具初始化 - 测试环境
2025-07-18 11:07:58 - INFO - 📋 表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 11:07:58 - INFO - 📄 工作表1: 仓库绩效明细
2025-07-18 11:07:58 - INFO - 📄 工作表2: 仓库绩效汇总
2025-07-18 11:07:58 - INFO - ✅ 获取钉钉访问令牌成功
2025-07-18 11:07:58 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 11:07:58 - INFO - ✅ Excel处理器初始化完成. 数据目录: D:/仓库发货数据
2025-07-18 11:07:58 - INFO - 🏭 开始处理日期为 2025-07-14 的数据（强制完整流程）...
2025-07-18 11:07:58 - INFO - 📊 开始处理最新的仓库状态数据文件...
2025-07-18 11:07:58 - INFO - ✅ 找到最新的 '各部门绩效数据' 文件: 各部门绩效数据.xlsx
2025-07-18 11:07:58 - INFO - - 成功读取 '各部门绩效数据.xlsx' 的 '义乌仓' sheet页
2025-07-18 11:07:58 - ERROR - E:\RPA项目\仓库发货数据拉取\excel_processor.py:361: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
  df[time_col] = pd.to_datetime(df[time_col], errors='coerce')
2025-07-18 11:07:58 - INFO - - 🔄 发现 43 个无法解析的日期，尝试中文格式解析...
2025-07-18 11:07:58 - INFO - - 🔄 重新读取原始Excel文件以获取未转换的日期数据...
2025-07-18 11:07:58 - INFO - - 📋 原始数据类型: <class 'str'>
2025-07-18 11:07:58 - INFO - - 📋 无法解析的日期样本: ['2025年06月27日 09:58:50', '2025年06月27日 10:14:11', '2025年06月28日 09:39:58']
2025-07-18 11:07:58 - INFO - - ✅ 成功解析 43 个中文日期格式
2025-07-18 11:07:58 - INFO - - ✅ 找到匹配的数据行，开始提取...
2025-07-18 11:07:58 - INFO - - 提取到数据，报告日期为: 2025/07/14, 数据时间戳: 2025-07-14 23:06:33
2025-07-18 11:07:58 - INFO - 📊 开始处理待处理订单文件...
2025-07-18 11:07:58 - INFO - ⚠️ 在目录 'D:/仓库发货数据' 中未找到包含'待处理订单'关键词的Excel文件
2025-07-18 11:07:58 - INFO - 🕚 强制按23点后完整流程执行：群通知 + 个人通知 + 表格写入
2025-07-18 11:07:58 - INFO - 📤 正在发送主报告到群...
2025-07-18 11:07:58 - INFO - ✅ 钉钉消息发送成功
2025-07-18 11:07:58 - INFO - ✅ 主报告发送成功！
2025-07-18 11:07:58 - INFO - 📊 奖惩计算基础数据：
2025-07-18 11:07:58 - INFO - 已审核-定制：208单
2025-07-18 11:07:58 - INFO - 已审核-现货：2803单
2025-07-18 11:07:58 - INFO - 已审核订单总计：3011单 (定制+现货)
2025-07-18 11:07:58 - INFO - 待补货订单：7单
2025-07-18 11:07:58 - INFO - 待处理订单：6单
2025-07-18 11:07:59 - INFO - 📤 正在发送奖惩报告到个人...
2025-07-18 11:07:59 - INFO - 📤 当前环境: 测试环境
2025-07-18 11:07:59 - INFO - 📤 发送目标: 杜陶祎（信息部） (userId: 01415968542326653172)
2025-07-18 11:07:59 - INFO - 📤 使用机器人发送个人消息给用户 01415968542326653172
2025-07-18 11:07:59 - INFO - 📋 消息标题: 奖惩计算结果
2025-07-18 11:07:59 - INFO - 📄 消息内容: 奖惩计算结果

基础数据：

已审核订单：3,011单

待补货订单：7单

待处理订单：6单

待补货奖惩（10元规则）：
条件：已审核≤4000且待补货≤15
结果：奖励 +10元

待处理奖惩（...
2025-07-18 11:07:59 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:00 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:00 - INFO - 📊 发送结果: {'flowControlledStaffIdList': [], 'invalidStaffIdList': [], 'processQueryKey': '6XaekznyOs8Pf9PY4k3xF5kffKwNSfgqpLMTyTKtF0g='}
2025-07-18 11:08:00 - INFO - ✅ 机器人消息发送成功
2025-07-18 11:08:00 - INFO - 任务查询键: 6XaekznyOs8Pf9PY4k3xF5kffKwNSfgqpLMTyTKtF0g=
2025-07-18 11:08:00 - INFO - ✅ 奖惩报告已发送给杜陶祎（信息部） - 测试环境
2025-07-18 11:08:00 - INFO - 📊 正在将报告块写入到钉钉表格...
2025-07-18 11:08:00 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:00 - INFO - 📝 追加日期为 2025/07/14 的历史数据到表格...
2025-07-18 11:08:00 - INFO - 🔍 配置文件无记录，开始全表扫描最后一行...
2025-07-18 11:08:00 - INFO - 🔍 正在高效查找最后一行数据（一次性读取最多 1000 行）...
2025-07-18 11:08:00 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:01 - INFO - 📍 找到最后数据行: 94
2025-07-18 11:08:01 - INFO - 📍 找到最后数据行: 94，下一个数据块将从第 96 行开始（留出1行空行分隔）
2025-07-18 11:08:01 - INFO - 🔗 正在合并分隔空行: A95:C95
2025-07-18 11:08:01 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:01 - INFO - ✅ 成功合并单元格: A95:C95
2025-07-18 11:08:01 - INFO - 🔍 调试信息:
2025-07-18 11:08:01 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE/sheets/kgqie6hm/ranges/A96:C113?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-07-18 11:08:01 - INFO - 数据: [['数据统计时间', '2025/07/18 11:08:01', ''], ['仓库状态数据', '', ''], ['类型', '品数', '订单数'], ['待补货', '9', '7'], ['待处理', '13', '6'], ['生产单完成进度', '', ''], ['已审核-定制', '', ''], ['已审核-现货', '', ''], ['发货数据', '', ''], ['现货订单数', '', ''], ['现货订单发货总重量', '', ''], ['定制订单数', '', ''], ['定制订单发货总重量', '', ''], ['奖惩计算', '', ''], ['待补货奖惩', '', ''], ['待处理奖惩', '', ''], ['总计奖惩', '', ''], ['奖惩计算结果', '', '']]
2025-07-18 11:08:01 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:01 - INFO - 响应状态码: 200
2025-07-18 11:08:01 - INFO - 响应内容: {"a1Notation":"A96:C113"}
2025-07-18 11:08:01 - INFO - ✅ 成功写入数据到工作表 kgqie6hm!A96:C113
2025-07-18 11:08:01 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:02 - INFO - ✅ 成功设置综合格式: A96:C113
2025-07-18 11:08:02 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:02 - INFO - ✅ 成功合并单元格: B96:C96
2025-07-18 11:08:02 - INFO - 🕐 检测到时间格式，设置为文本格式: 2025/07/18 11:08:01
2025-07-18 11:08:02 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:02 - INFO - ✅ 成功设置合并单元格的值和格式: B96 = '2025/07/18 11:08:01'
2025-07-18 11:08:02 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:02 - INFO - ✅ 成功合并单元格: A97:C97
2025-07-18 11:08:02 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:02 - INFO - ✅ 成功设置合并单元格的值和格式: A97 = '仓库状态数据'
2025-07-18 11:08:02 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:03 - INFO - ✅ 成功合并单元格: B101:C101
2025-07-18 11:08:03 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:03 - INFO - ✅ 成功设置合并单元格的值和格式: B101 = '未完成：3/8，已完成：5/8'
2025-07-18 11:08:03 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:03 - INFO - ✅ 成功合并单元格: B102:C102
2025-07-18 11:08:03 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:03 - INFO - ✅ 成功设置合并单元格的值和格式: B102 = '208'
2025-07-18 11:08:03 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:03 - INFO - ✅ 成功合并单元格: B103:C103
2025-07-18 11:08:03 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:04 - INFO - ✅ 成功设置合并单元格的值和格式: B103 = '2803'
2025-07-18 11:08:04 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:04 - INFO - ✅ 成功合并单元格: A104:C104
2025-07-18 11:08:04 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:04 - INFO - ✅ 成功设置合并单元格的值和格式: A104 = '发货数据'
2025-07-18 11:08:04 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:04 - INFO - ✅ 成功合并单元格: B105:C105
2025-07-18 11:08:04 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:04 - INFO - ✅ 成功设置合并单元格的值和格式: B105 = '0'
2025-07-18 11:08:04 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:05 - INFO - ✅ 成功合并单元格: B106:C106
2025-07-18 11:08:05 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:05 - INFO - ✅ 成功设置合并单元格的值和格式: B106 = '0.0kg'
2025-07-18 11:08:05 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:05 - INFO - ✅ 成功合并单元格: B107:C107
2025-07-18 11:08:05 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:05 - INFO - ✅ 成功设置合并单元格的值和格式: B107 = '0'
2025-07-18 11:08:05 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:05 - INFO - ✅ 成功合并单元格: B108:C108
2025-07-18 11:08:06 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:06 - INFO - ✅ 成功设置合并单元格的值和格式: B108 = '0.0kg'
2025-07-18 11:08:06 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:06 - INFO - ✅ 成功合并单元格: A109:C109
2025-07-18 11:08:06 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:06 - INFO - ✅ 成功设置合并单元格的值和格式: A109 = '奖惩计算'
2025-07-18 11:08:06 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:06 - INFO - ✅ 成功合并单元格: B110:C110
2025-07-18 11:08:06 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:06 - INFO - ✅ 成功设置合并单元格的值和格式: B110 = '奖励 +10元'
2025-07-18 11:08:07 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:07 - INFO - ✅ 成功合并单元格: B111:C111
2025-07-18 11:08:07 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:07 - INFO - ✅ 成功设置合并单元格的值和格式: B111 = '奖励 +25元'
2025-07-18 11:08:07 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:07 - INFO - ✅ 成功合并单元格: B112:C112
2025-07-18 11:08:07 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:07 - INFO - ✅ 成功设置合并单元格的值和格式: B112 = '+35元'
2025-07-18 11:08:07 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:07 - INFO - ✅ 成功合并单元格: A113:C113
2025-07-18 11:08:08 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:08 - INFO - ✅ 成功设置合并单元格的值和格式: A113 = '已审核订单：3,011单 待补货订单：7单 待处理订单：6单 | 待补货奖惩（10元规则）：已审核≤4000且待补货≤15 结果：奖励 10元 | 待处理奖惩（25元规则）：已审核≤4000且待处理≤30（1%） 结果：奖励 25元 | 总计奖惩：+35元'
2025-07-18 11:08:08 - INFO - ℹ️ 钉钉表格API不支持边框设置，跳过边框设置: A96:C113
2025-07-18 11:08:08 - INFO - ✅ 成功追加仓库报告块到第 96-113 行
2025-07-18 11:08:08 - INFO - ✅ 历史数据成功写入钉钉表格！
2025-07-18 11:08:08 - INFO - 📝 已更新测试环境配置文件，记录最后一行: 113
2025-07-18 11:08:08 - INFO - 📊 开始生成月度汇总数据...
2025-07-18 11:08:08 - INFO - 🚀 开始自动运行月度汇总流程...
2025-07-18 11:08:08 - INFO - ✅ 配置文件加载成功: config.json
2025-07-18 11:08:08 - INFO - 🔧 钉钉表格工具初始化 - 测试环境
2025-07-18 11:08:08 - INFO - 📋 表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 11:08:08 - INFO - 📄 工作表1: 仓库绩效明细
2025-07-18 11:08:08 - INFO - 📄 工作表2: 仓库绩效汇总
2025-07-18 11:08:08 - INFO - ✅ 获取钉钉访问令牌成功
2025-07-18 11:08:08 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 11:08:08 - INFO - ✅ 钉钉表格工具初始化完成
2025-07-18 11:08:08 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:08 - INFO - 📋 使用工作表: 仓库绩效明细 (kgqie6hm)
2025-07-18 11:08:08 - INFO - 🔍 开始读取仓库绩效明细数据...
2025-07-18 11:08:08 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:09 - INFO - 📊 读取到 500 行原始数据
2025-07-18 11:08:09 - INFO - ✅ 成功解析 6 条有效数据记录
2025-07-18 11:08:09 - INFO - 📊 读取到 6 条有效数据记录
2025-07-18 11:08:09 - INFO - 📊 开始生成月度汇总...
2025-07-18 11:08:09 - INFO - 📅 生成了 1 个月的汇总数据
2025-07-18 11:08:09 - INFO - 📝 开始写入Sheet2...
2025-07-18 11:08:09 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:09 - INFO - 📋 开始写入仓库绩效汇总: st-3a20f016-86121
2025-07-18 11:08:09 - INFO - 📝 准备写入数据...
2025-07-18 11:08:09 - INFO - 📝 准备 2025年07月数据汇总 数据...
2025-07-18 11:08:09 - INFO - 📊 开始写入 9 行数据到仓库绩效汇总...
2025-07-18 11:08:09 - INFO - 🔍 调试信息:
2025-07-18 11:08:09 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE/sheets/st-3a20f016-86121/ranges/A1:L9?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-07-18 11:08:09 - INFO - 数据: [['2025年07月数据汇总', '', '', '', '', '', '', '', '', '', '', ''], ['日期', '待补货品数', '待补货订单', '待处理品数', '待处理订单', '现货订单', '定制订单', '发货重量', '已审核定制', '已审核现货', '生产进度', '奖惩金额'], ['07-16', '1', '1', '6', '6', '1306', '453', '100875.0', '205', '2730', '未完成：0/9，已完成：9/9', '+35元'], ['07-18', '0', '0', '0', '0', '2644', '186', '7829.78', '164', '2688', '未完成：5/13，已完成：8/13', '+35元'], ['07-18', '0', '0', '0', '0', '2644', '186', '7829.78', '164', '2688', '未完成：5/13，已完成：8/13', '+35元'], ['07-18', '0', '0', '0', '0', '0', '0', '0.0', '208', '2803', '未完成：3/8，已完成：5/8', '+35元'], ['07-18', '9', '7', '13', '6', '0', '0', '0.0', '208', '2803', '未完成：3/8，已完成：5/8', '+35元'], ['07-18', '9', '7', '13', '6', '0', '0', '0.0', '208', '2803', '未完成：3/8，已完成：5/8', '+35元'], ['', '', '', '', '', '', '', '', '', '', '', '']]
2025-07-18 11:08:09 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:09 - INFO - 响应状态码: 200
2025-07-18 11:08:09 - INFO - 响应内容: {"a1Notation":"A1:L9"}
2025-07-18 11:08:09 - INFO - ✅ 成功写入数据到工作表 st-3a20f016-86121!A1:L9
2025-07-18 11:08:09 - INFO - ✅ 数据写入成功
2025-07-18 11:08:09 - INFO - 🎨 应用格式设置...
2025-07-18 11:08:09 - INFO - 📐 设置所有数据居中对齐...
2025-07-18 11:08:09 - INFO - 🔍 格式设置调试:
2025-07-18 11:08:09 - INFO - URL: https://api.dingtalk.com/v1.0/doc/workbooks/gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE/sheets/st-3a20f016-86121/ranges/A1:L9?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-07-18 11:08:09 - INFO - 格式数据: {'horizontalAlignments': [['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center'], ['center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center', 'center']], 'verticalAlignments': [['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle'], ['middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle', 'middle']]}
2025-07-18 11:08:10 - INFO - 响应状态码: 200
2025-07-18 11:08:10 - INFO - 响应内容: {"a1Notation":"A1:L9"}
2025-07-18 11:08:10 - INFO - 🔗 合并标题行: A1:L1
2025-07-18 11:08:10 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:10 - INFO - ✅ 成功合并单元格: A1:L1
2025-07-18 11:08:10 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:08:10 - INFO - ✅ 成功设置合并单元格的值和格式: A1 = '2025年07月数据汇总'
2025-07-18 11:08:10 - INFO - ✅ 成功合并标题: 2025年07月数据汇总
2025-07-18 11:08:10 - INFO - 🎉 月度汇总数据已成功写入Sheet2！
2025-07-18 11:08:10 - INFO - ✅ 月度汇总数据生成完成
2025-07-18 11:08:10 - INFO - 🎉 日期 2025-07-14 的完整流程处理完成！
2025-07-18 11:08:10 - INFO - ✅ 日期 2025-07-14 的完整流程重新执行成功！
2025-07-18 11:09:46 - INFO - 日志记录已初始化，stdout和stderr现已重定向到日志文件。
2025-07-18 11:09:46 - INFO - 🏭 仓库发货数据钉钉通知程序
2025-07-18 11:09:46 - INFO - ========================================
2025-07-18 11:09:46 - INFO - 🔄 重新执行模式：处理日期 2025-07-15 的完整流程
2025-07-18 11:09:46 - INFO - 📅 目标时间设定为: 2025-07-15 23:00:00
2025-07-18 11:09:46 - INFO - ⚠️  将按23点后的完整流程执行（群通知+个人通知+表格写入）
2025-07-18 11:09:46 - INFO - 🔑 正在获取钉钉access_token...
2025-07-18 11:09:46 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:09:46 - INFO - ✅ 获取access_token成功，有效期: 7200秒
2025-07-18 11:09:46 - INFO - 🔧 钉钉表格工具初始化 - 测试环境
2025-07-18 11:09:46 - INFO - 📋 表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 11:09:46 - INFO - 📄 工作表1: 仓库绩效明细
2025-07-18 11:09:46 - INFO - 📄 工作表2: 仓库绩效汇总
2025-07-18 11:09:46 - INFO - ✅ 获取钉钉访问令牌成功
2025-07-18 11:09:46 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 11:09:46 - INFO - ✅ Excel处理器初始化完成. 数据目录: D:/仓库发货数据
2025-07-18 11:09:46 - INFO - 🏭 开始处理日期为 2025-07-15 的数据（强制完整流程）...
2025-07-18 11:09:46 - INFO - 📊 开始处理最新的仓库状态数据文件...
2025-07-18 11:09:46 - INFO - ✅ 找到最新的 '各部门绩效数据' 文件: 各部门绩效数据.xlsx
2025-07-18 11:09:47 - INFO - - 成功读取 '各部门绩效数据.xlsx' 的 '义乌仓' sheet页
2025-07-18 11:09:47 - ERROR - E:\RPA项目\仓库发货数据拉取\excel_processor.py:361: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.
  df[time_col] = pd.to_datetime(df[time_col], errors='coerce')
2025-07-18 11:09:47 - INFO - - 🔄 发现 43 个无法解析的日期，尝试中文格式解析...
2025-07-18 11:09:47 - INFO - - 🔄 重新读取原始Excel文件以获取未转换的日期数据...
2025-07-18 11:09:47 - INFO - - 📋 原始数据类型: <class 'str'>
2025-07-18 11:09:47 - INFO - - 📋 无法解析的日期样本: ['2025年06月27日 09:58:50', '2025年06月27日 10:14:11', '2025年06月28日 09:39:58']
2025-07-18 11:09:47 - INFO - - ✅ 成功解析 43 个中文日期格式
2025-07-18 11:09:47 - INFO - - ✅ 找到匹配的数据行，开始提取...
2025-07-18 11:09:47 - INFO - - 提取到数据，报告日期为: 2025/07/15, 数据时间戳: 2025-07-15 23:06:37
2025-07-18 11:09:47 - INFO - 📊 开始处理待处理订单文件...
2025-07-18 11:09:47 - INFO - ⚠️ 在目录 'D:/仓库发货数据' 中未找到包含'待处理订单'关键词的Excel文件
2025-07-18 11:09:47 - INFO - 🕚 强制按23点后完整流程执行：群通知 + 个人通知 + 表格写入
2025-07-18 11:09:47 - INFO - 📤 正在发送主报告到群...
2025-07-18 11:09:47 - INFO - ✅ 钉钉消息发送成功
2025-07-18 11:09:47 - INFO - ✅ 主报告发送成功！
2025-07-18 11:09:47 - INFO - 📊 奖惩计算基础数据：
2025-07-18 11:09:47 - INFO - 已审核-定制：205单
2025-07-18 11:09:47 - INFO - 已审核-现货：2795单
2025-07-18 11:09:47 - INFO - 已审核订单总计：3000单 (定制+现货)
2025-07-18 11:09:47 - INFO - 待补货订单：25单
2025-07-18 11:09:47 - INFO - 待处理订单：5单
2025-07-18 11:09:48 - INFO - 📤 正在发送奖惩报告到个人...
2025-07-18 11:09:48 - INFO - 📤 当前环境: 测试环境
2025-07-18 11:09:48 - INFO - 📤 发送目标: 杜陶祎（信息部） (userId: 01415968542326653172)
2025-07-18 11:09:48 - INFO - 📤 使用机器人发送个人消息给用户 01415968542326653172
2025-07-18 11:09:48 - INFO - 📋 消息标题: 奖惩计算结果
2025-07-18 11:09:48 - INFO - 📄 消息内容: 奖惩计算结果

基础数据：

已审核订单：3,000单

待补货订单：25单

待处理订单：5单

待补货奖惩（10元规则）：
条件：已审核≤4000且待补货≤15
结果：惩罚 -10元

待处理奖惩...
2025-07-18 11:09:48 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:09:48 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:09:48 - INFO - 📊 发送结果: {'flowControlledStaffIdList': [], 'invalidStaffIdList': [], 'processQueryKey': '1usXj6a8UkyCUQKpURNXMEzbUK5Po4B5BiSrVWgfhgc='}
2025-07-18 11:09:48 - INFO - ✅ 机器人消息发送成功
2025-07-18 11:09:48 - INFO - 任务查询键: 1usXj6a8UkyCUQKpURNXMEzbUK5Po4B5BiSrVWgfhgc=
2025-07-18 11:09:48 - INFO - ✅ 奖惩报告已发送给杜陶祎（信息部） - 测试环境
2025-07-18 11:09:48 - INFO - 📊 正在将报告块写入到钉钉表格...
2025-07-18 11:09:48 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:09:54 - INFO - ❌ 获取工作表列表失败: 503 Server Error: Service Unavailable for url: https://api.dingtalk.com/v1.0/doc/workbooks/gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE/sheets?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-07-18 11:09:54 - INFO - ❌ 无法获取钉钉表格工作表列表
2025-07-18 11:09:54 - INFO - 📊 开始生成月度汇总数据...
2025-07-18 11:09:54 - INFO - 🚀 开始自动运行月度汇总流程...
2025-07-18 11:09:54 - INFO - ✅ 配置文件加载成功: config.json
2025-07-18 11:09:54 - INFO - 🔧 钉钉表格工具初始化 - 测试环境
2025-07-18 11:09:54 - INFO - 📋 表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 11:09:54 - INFO - 📄 工作表1: 仓库绩效明细
2025-07-18 11:09:54 - INFO - 📄 工作表2: 仓库绩效汇总
2025-07-18 11:09:55 - INFO - ✅ 获取钉钉访问令牌成功
2025-07-18 11:09:55 - INFO - ✅ 钉钉表格工具初始化完成，表格ID: gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE
2025-07-18 11:09:55 - INFO - ✅ 钉钉表格工具初始化完成
2025-07-18 11:09:55 - ERROR - C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
2025-07-18 11:10:00 - INFO - ❌ 获取工作表列表失败: 500 Server Error: Server Error for url: https://api.dingtalk.com/v1.0/doc/workbooks/gvNG4YZ7JnE99q1dHyq6w3KOV2LD0oRE/sheets?operatorId=SslfRpH3glxew7XC8gCkdgiEiE
2025-07-18 11:10:00 - INFO - ❌ 无法获取工作表列表
2025-07-18 11:10:00 - INFO - ✅ 月度汇总数据生成完成
2025-07-18 11:10:00 - INFO - 🎉 日期 2025-07-15 的完整流程处理完成！
2025-07-18 11:10:00 - INFO - ✅ 日期 2025-07-15 的完整流程重新执行成功！
