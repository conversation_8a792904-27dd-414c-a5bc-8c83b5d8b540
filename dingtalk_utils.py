import time
import hmac
import hashlib
import base64
import urllib.parse
import requests
import json
import os


class DingTalkUtils:
    def __init__(self, webhook_url, secret=None, access_token=None, agent_id=None):
        """
        初始化钉钉工具类

        Args:
            webhook_url:https://oapi.dingtalk.com/robot/send?access_token=42b6a1c54aa934c1baf7ac8a53e1ca481d01f98df0b9415894ba9a14acd6c30a
            secret:SEC380a8bb05198efe60001f3561d0c69ba51d02e35f50c1348a450fc6ed1636b9c
            access_token: 企业应用的access_token（用于发送工作通知）
            agent_id: 应用的AgentID（用于发送工作通知）
        """
        self.webhook_url = webhook_url
        self.secret = secret
        self.access_token = access_token
        self.agent_id = agent_id
    
    def _generate_sign(self, timestamp):
        """
        生成钉钉签名
        
        Args:
            timestamp: 时间戳
            
        Returns:
            签名字符串
        """
        if not self.secret:
            return None
            
        string_to_sign = f"{timestamp}\n{self.secret}"
        hmac_code = hmac.new(
            self.secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
        return sign
    
    def send_markdown_message(self, title, text):
        """
        发送Markdown格式消息
        
        Args:
            title: 消息标题
            text: Markdown格式的消息内容
            
        Returns:
            响应结果
        """
        # 准备请求URL
        url = self.webhook_url
        
        # 如果有签名密钥，添加签名参数
        if self.secret:
            timestamp = str(round(time.time() * 1000))
            sign = self._generate_sign(timestamp)
            if sign:
                url += f"&timestamp={timestamp}&sign={sign}"
        
        # 准备消息数据
        data = {
            "msgtype": "markdown",
            "markdown": {
                "title": title,
                "text": text
            }
        }
        
        # 发送请求
        headers = {'Content-Type': 'application/json'}
        try:
            response = requests.post(url, data=json.dumps(data), headers=headers)
            response.raise_for_status()
            result = response.json()
            
            if result.get('errcode') == 0:
                print("✅ 钉钉消息发送成功")
                return True, result
            else:
                print(f"❌ 钉钉消息发送失败: {result.get('errmsg', '未知错误')}")
                return False, result
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求错误: {str(e)}")
            return False, {"error": str(e)}
        except Exception as e:
            print(f"❌ 发送消息时出现错误: {str(e)}")
            return False, {"error": str(e)}
    

    
    def send_text_message(self, content):
        """
        发送文本消息
        
        Args:
            content: 文本内容
            
        Returns:
            响应结果
        """
        # 准备请求URL
        url = self.webhook_url
        
        # 如果有签名密钥，添加签名参数
        if self.secret:
            timestamp = str(round(time.time() * 1000))
            sign = self._generate_sign(timestamp)
            if sign:
                url += f"&timestamp={timestamp}&sign={sign}"
        
        # 准备消息数据
        data = {
            "msgtype": "text",
            "text": {
                "content": content
            }
        }
        
        # 发送请求
        headers = {'Content-Type': 'application/json'}
        try:
            response = requests.post(url, data=json.dumps(data), headers=headers)
            response.raise_for_status()
            result = response.json()
            
            if result.get('errcode') == 0:
                print("✅ 钉钉消息发送成功")
                return True, result
            else:
                print(f"❌ 钉钉消息发送失败: {result.get('errmsg', '未知错误')}")
                return False, result
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求错误: {str(e)}")
            return False, {"error": str(e)}
        except Exception as e:
            print(f"❌ 发送消息时出现错误: {str(e)}")
            return False, {"error": str(e)}

    def send_work_notice(self, userid, title, content):
        """
        发送工作通知给指定用户

        Args:
            userid: 用户ID
            title: 消息标题
            content: 消息内容（支持markdown格式）

        Returns:
            (是否成功, 响应结果)
        """
        if not self.access_token or not self.agent_id:
            print("❌ 缺少access_token或agent_id，无法发送工作通知")
            return False, {"error": "缺少access_token或agent_id"}

        # 构造API URL
        url = f"https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2?access_token={self.access_token}"

        # 构造消息数据
        data = {
            "agent_id": self.agent_id,
            "userid_list": userid,
            "to_all_user": False,
            "msg": {
                "msgtype": "markdown",
                "markdown": {
                    "title": title,
                    "text": content
                }
            }
        }

        # 发送请求
        headers = {'Content-Type': 'application/json'}
        try:
            response = requests.post(url, data=json.dumps(data), headers=headers, verify=False, timeout=30)
            response.raise_for_status()
            result = response.json()

            if result.get('errcode') == 0:
                print(f"✅ 工作通知发送成功，任务ID: {result.get('task_id')}")
                return True, result
            else:
                print(f"❌ 工作通知发送失败: {result.get('errmsg', '未知错误')}")
                return False, result

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求错误: {str(e)}")
            return False, {"error": str(e)}
        except Exception as e:
            print(f"❌ 发送工作通知时出现错误: {str(e)}")
            return False, {"error": str(e)}

    @staticmethod
    def get_access_token(app_key, app_secret):
        """
        获取企业内部应用的access_token

        Args:
            app_key: 应用的AppKey
            app_secret: 应用的AppSecret

        Returns:
            (是否成功, access_token或错误信息)
        """
        url = "https://oapi.dingtalk.com/gettoken"
        params = {
            "appkey": app_key,
            "appsecret": app_secret
        }

        try:
            response = requests.get(url, params=params, verify=False, timeout=30)
            response.raise_for_status()
            result = response.json()

            if result.get('errcode') == 0:
                access_token = result.get('access_token')
                expires_in = result.get('expires_in')
                print(f"✅ 获取access_token成功，有效期: {expires_in}秒")
                return True, access_token
            else:
                print(f"❌ 获取access_token失败: {result.get('errmsg', '未知错误')}")
                return False, result.get('errmsg', '未知错误')

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求错误: {str(e)}")
            return False, str(e)
        except Exception as e:
            print(f"❌ 获取access_token时出现错误: {str(e)}")
            return False, str(e)