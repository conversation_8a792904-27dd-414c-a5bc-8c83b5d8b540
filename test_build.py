#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试打包优化效果的脚本
"""

import os
import subprocess
import time
from pathlib import Path

def get_file_size_mb(file_path):
    """获取文件大小（MB）"""
    if os.path.exists(file_path):
        size_bytes = os.path.getsize(file_path)
        return round(size_bytes / (1024 * 1024), 2)
    return 0

def clean_build_files():
    """清理构建文件"""
    print("🧹 清理旧的构建文件...")
    
    # 删除dist目录
    if os.path.exists("dist"):
        import shutil
        shutil.rmtree("dist")
        print("   - 删除 dist/ 目录")
    
    # 删除build目录
    if os.path.exists("build"):
        import shutil
        shutil.rmtree("build")
        print("   - 删除 build/ 目录")
    
    # 删除.spec文件
    spec_files = list(Path(".").glob("*.spec"))
    for spec_file in spec_files:
        spec_file.unlink()
        print(f"   - 删除 {spec_file}")

def main():
    print("🚀 开始测试打包优化效果...")
    
    # 清理旧文件
    clean_build_files()
    
    # 记录开始时间
    start_time = time.time()
    
    # 执行打包
    print("\n📦 开始执行优化打包...")
    try:
        # 使用更兼容的编码处理方式
        result = subprocess.run(["python", "build_exe.py"],
                              capture_output=True,
                              text=True,
                              encoding='utf-8',
                              errors='ignore')  # 忽略编码错误

        if result.returncode == 0:
            print("✅ 打包成功完成！")
            if result.stdout:
                print("📝 打包输出：")
                print(result.stdout)
        else:
            print("❌ 打包过程中出现错误：")
            if result.stderr:
                print(result.stderr)
            if result.stdout:
                print("标准输出：")
                print(result.stdout)
            return

    except Exception as e:
        print(f"❌ 执行打包脚本失败: {str(e)}")
        return
    
    # 计算耗时
    end_time = time.time()
    duration = round(end_time - start_time, 2)
    
    # 检查生成的exe文件
    exe_path = "dist/仓库发货数据通知.exe"
    if os.path.exists(exe_path):
        file_size = get_file_size_mb(exe_path)
        print(f"\n📊 打包结果统计：")
        print(f"   📁 文件路径: {exe_path}")
        print(f"   📏 文件大小: {file_size} MB")
        print(f"   ⏱️ 打包耗时: {duration} 秒")
        
        # 评估优化效果
        if file_size < 50:
            print(f"   🎉 优化效果: 优秀！文件大小控制在50MB以内")
        elif file_size < 100:
            print(f"   👍 优化效果: 良好！文件大小控制在100MB以内")
        elif file_size < 150:
            print(f"   ⚠️ 优化效果: 一般，文件大小仍然较大")
        else:
            print(f"   ❌ 优化效果: 不佳，需要进一步优化")
            
    else:
        print("❌ 未找到生成的exe文件")

if __name__ == "__main__":
    main()
