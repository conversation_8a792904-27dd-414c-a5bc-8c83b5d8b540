import logging
import sys
from logging.handlers import TimedRotatingFileHandler


class LoggerWriter:
    def __init__(self, logger, level):
        self.logger = logger
        self.level = level

    def write(self, message):
        if message.strip():
            self.logger.log(self.level, message.strip())

    def flush(self):
        pass


def setup_logging(config):
    """
    Configures logging and redirects stdout/stderr.
    """
    log_config = config.get('logging')
    if not log_config or not log_config.get('enabled'):
        return

    log_file = log_config.get('log_file', 'warehouse_report.log')
    log_level = log_config.get('log_level', 'INFO')
    retention_days = log_config.get('retention_days', 3)

    logger = logging.getLogger()
    logger.setLevel(log_level)
    
    if logger.hasHandlers():
        logger.handlers.clear()

    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')

    console_handler = logging.StreamHandler(sys.__stdout__)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    file_handler = TimedRotatingFileHandler(
        log_file, when='midnight', backupCount=retention_days, encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    sys.stdout = LoggerWriter(logger, logging.INFO)
    sys.stderr = LoggerWriter(logger, logging.ERROR)

    logger.info("日志记录已初始化，stdout和stderr现已重定向到日志文件。") 