# 钉钉表格功能使用说明

## 功能概述

本程序新增了将仓库发货数据自动写入钉钉在线表格的功能。除了原有的钉钉消息通知外，现在还可以将数据结构化地保存到钉钉表格中，便于数据分析和历史记录查看。

## 配置步骤

### 1. 获取钉钉应用凭证

1. 登录钉钉开发者后台：https://open-dev.dingtalk.com/fe/daas
2. 创建或选择一个企业内部应用
3. 获取以下信息：
   - **Client ID (AppKey)**：应用的唯一标识
   - **Client Secret (AppSecret)**：应用的密钥
   - **操作者ID (operator_id)**：汪波的userId是 `0239410014888248`

### 2. 配置权限

确保钉钉应用具有以下权限：
- 文档管理权限
- 在线表格读写权限

### 3. 修改配置文件

在 `config.json` 文件中添加钉钉表格配置：

```json
{
  "dingtalk": {
    "webhook_url": "现有的webhook地址",
    "secret": "现有的secret",
    "enabled": true,
    "sheet": {
      "enabled": true,
      "app_key": "你的AppKey",
      "app_secret": "你的AppSecret", 
      "operator_id": "0239410014888248",
      "sheet_id": "KGZLxjv9VGZggx9vT70ngwGrW6EDybno",
      "sheet_name": "Sheet1"
    }
  }
}
```

### 4. 配置参数说明

- `enabled`: 是否启用钉钉表格功能
- `app_key`: 钉钉应用的AppKey
- `app_secret`: 钉钉应用的AppSecret
- `operator_id`: 操作者的userId（汪波的ID）
- `sheet_id`: 目标表格的ID（已提供）
- `sheet_name`: 工作表名称（默认Sheet1）

## 功能特性

### 1. 自动数据写入

程序运行时会自动将以下数据写入钉钉表格：

- **时间戳**：数据生成时间
- **仓库状态数据**：
  - 待补货（品数、订单数）
  - 待处理（品数、订单数）
  - 生产单未完成（订单数）
  - 已审核（订单数）
- **发货数据**：
  - 订单数
  - 发货总金额
- **待处理订单列表**（如果存在且达到时间阈值）

### 2. 数据格式

表格中的数据按以下格式组织：

```
数据生成时间    2025-01-07 14:30:00

仓库状态数据
类型          品数    订单数
待补货        15      8
待处理        23      12
生产单未完成          5
已审核               45

发货数据
订单数        128
发货总金额    25680.50

待处理订单列表
ORD001       ORD002
ORD003       ORD004
...
```

### 3. 错误处理

- 如果钉钉表格功能配置错误，程序会继续执行其他功能
- 表格写入失败不会影响钉钉消息发送
- 详细的错误信息会在控制台显示

## 测试功能

### 运行测试脚本

```bash
python test_dingtalk_sheet.py
```

测试脚本会验证：
1. 基本的读写操作
2. DataFrame数据写入
3. 仓库报告数据写入

### 测试步骤

1. 确保配置文件正确
2. 运行测试脚本
3. 检查钉钉表格中是否有测试数据
4. 验证数据格式是否正确

## 使用方法

### 1. 启用功能

在 `config.json` 中设置：
```json
"sheet": {
  "enabled": true,
  ...
}
```

### 2. 运行主程序

```bash
python warehouse_report.py
```

程序会：
1. 处理Excel数据
2. 发送钉钉消息通知
3. 将数据写入钉钉表格

### 3. 禁用功能

如果不需要表格功能，设置：
```json
"sheet": {
  "enabled": false,
  ...
}
```

## 注意事项

### 1. 权限要求

- 确保操作者（汪波）对目标表格有写入权限
- 钉钉应用需要有文档管理权限

### 2. 数据覆盖

- 每次运行会清空表格并写入新数据
- 如需保留历史数据，建议使用不同的工作表

### 3. 网络要求

- 需要稳定的网络连接访问钉钉API
- 建议在网络良好的环境下运行

### 4. 错误排查

如果遇到问题：
1. 检查配置文件格式是否正确
2. 验证AppKey和AppSecret是否有效
3. 确认操作者ID是否正确
4. 检查表格ID是否存在且可访问

## API限制

- 单次写入最大1000行数据
- 程序会自动分批处理大量数据
- 建议避免频繁调用以免触发限流

## 技术支持

如有问题，请检查：
1. 控制台错误信息
2. 钉钉开发者后台的应用状态
3. 网络连接状况
4. 配置文件格式
