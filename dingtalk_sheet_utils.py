#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
钉钉在线表格工具类
用于向钉钉在线表格写入数据
"""

import requests
import json
from datetime import datetime
from typing import List, Dict, Any
import re


class DingTalkSheetUtils:
    def __init__(self, config: Dict):
        """
        初始化钉钉表格工具类

        Args:
            config: 完整的配置字典
        """
        # 从配置中获取基础信息
        sheet_config = config.get('dingtalk', {}).get('sheet', {})
        self.app_key = sheet_config.get('app_key')
        self.app_secret = sheet_config.get('app_secret')
        self.operator_id = sheet_config.get('operator_id')

        # 根据当前环境选择表格配置
        current_env = config.get('dingtalk', {}).get('current_environment', 'prod')
        if current_env == 'test':
            env_sheet_config = sheet_config.get('test_sheet', {})
            self.env_name = "测试环境"
        else:
            env_sheet_config = sheet_config.get('prod_sheet', {})
            self.env_name = "正式环境"

        self.sheet_id = env_sheet_config.get('sheet_id')
        self.sheet_name = env_sheet_config.get('sheet_name', '仓库绩效明细')
        self.sheet2_name = env_sheet_config.get('sheet2_name', '仓库绩效汇总')
        self.last_row = env_sheet_config.get('last_row', 1)

        # 保存完整配置用于更新last_row
        self.config = config
        self.current_env = current_env

        self.access_token = None
        self.primary_domain = "https://api.dingtalk.com/v1.0/doc/workbooks/"

        print(f"🔧 钉钉表格工具初始化 - {self.env_name}")
        print(f"📋 表格ID: {self.sheet_id}")
        print(f"📄 工作表1: {self.sheet_name}")
        print(f"📄 工作表2: {self.sheet2_name}")

        # 获取access_token
        self._get_access_token()

        # 设置基础请求头（简化配置，避免网络问题）
        self.default_headers = {
            'Content-Type': 'application/json',
            'x-acs-dingtalk-access-token': self.access_token
        }
        
        print(f"✅ 钉钉表格工具初始化完成，表格ID: {self.sheet_id}")
    
    def _get_access_token(self):
        """获取钉钉访问令牌"""
        try:
            url = f"https://oapi.dingtalk.com/gettoken?appkey={self.app_key}&appsecret={self.app_secret}"
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            result = response.json()
            
            if 'access_token' in result:
                self.access_token = result['access_token']
                print("✅ 获取钉钉访问令牌成功")
            else:
                raise Exception(f"获取访问令牌失败: {result}")
                
        except Exception as e:
            print(f"❌ 获取钉钉访问令牌失败: {str(e)}")
            raise

    def update_last_row(self, new_last_row: int):
        """
        更新配置文件中的last_row

        Args:
            new_last_row: 新的最后一行行号
        """
        try:
            # 根据当前环境更新对应的last_row
            if self.current_env == 'test':
                self.config['dingtalk']['sheet']['test_sheet']['last_row'] = new_last_row
            else:
                self.config['dingtalk']['sheet']['prod_sheet']['last_row'] = new_last_row

            # 保存配置文件
            import json
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)

            print(f"📝 已更新{self.env_name}配置文件，记录最后一行: {new_last_row}")

        except Exception as e:
            print(f"❌ 更新配置文件失败: {str(e)}")
    
    def get_sheets_list(self) -> List[Dict]:
        """
        获取工作表列表

        Returns:
            工作表列表
        """
        try:
            url = f"{self.primary_domain}{self.sheet_id}/sheets?operatorId={self.operator_id}"

            response = requests.get(url, headers=self.default_headers, verify=False, timeout=10)
            response.raise_for_status()
            result = response.json()

            return result.get("value", [])

        except Exception as e:
            print(f"❌ 获取工作表列表失败: {str(e)}")
            return []

    def update_sheet_name(self, sheet_id: str, new_name: str) -> bool:
        """
        修改工作表名称

        Args:
            sheet_id: 工作表ID
            new_name: 新的工作表名称

        Returns:
            是否成功
        """
        try:
            url = f"{self.primary_domain}{self.sheet_id}/sheets/{sheet_id}?operatorId={self.operator_id}"

            data = {
                "name": new_name
            }

            response = requests.put(url, headers=self.default_headers, json=data, verify=False, timeout=10)

            if response.status_code == 200:
                print(f"✅ 成功修改工作表名称: {sheet_id} -> {new_name}")
                return True
            else:
                print(f"❌ 修改工作表名称失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print(f"❌ 修改工作表名称异常: {str(e)}")
            return False

    def add_sheet(self, sheet_name: str) -> Dict:
        """
        在工作簿末尾添加一个新的工作表

        Args:
            sheet_name: 新工作表的名称

        Returns:
            新工作表的信息字典，失败则返回None
        """
        try:
            url = f"{self.primary_domain}{self.sheet_id}/sheets?operatorId={self.operator_id}"
            data = {"name": sheet_name}

            response = requests.post(url, headers=self.default_headers, json=data, timeout=10)

            if response.status_code == 200:
                new_sheet_info = response.json()
                print(f"✅ 成功添加新工作表: '{sheet_name}' (ID: {new_sheet_info.get('id')})")
                return new_sheet_info
            else:
                print(f"❌ 添加工作表失败: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"❌ 添加工作表时发生异常: {str(e)}")
            return None

    def get_cell_range(self, sheet_id: str, cell_range: str) -> List[List[str]]:
        """
        获取单元格区域内容

        Args:
            sheet_id: 工作表ID（不是名称）
            cell_range: 单元格范围，如 "A1:C10"

        Returns:
            单元格内容的二维列表
        """
        try:
            url = (f"{self.primary_domain}{self.sheet_id}"
                   f"/sheets/{sheet_id}/ranges/{cell_range}"
                   f"?operatorId={self.operator_id}")

            response = requests.get(url, headers=self.default_headers, verify=False, timeout=30)
            response.raise_for_status()
            result = response.json()

            return result.get("displayValues", [])

        except Exception as e:
            print(f"❌ 获取单元格内容失败: {str(e)}")
            return []
    
    def write_cell_range(self, sheet_id: str, cell_range: str, values: List[List[Any]]) -> bool:
        """
        写入单元格区域内容

        Args:
            sheet_id: 工作表ID
            cell_range: 单元格范围，如 "A1:C10"
            values: 要写入的数据，二维列表

        Returns:
            是否成功
        """
        try:
            url = (f"{self.primary_domain}{self.sheet_id}"
                   f"/sheets/{sheet_id}/ranges/{cell_range}"
                   f"?operatorId={self.operator_id}")

            print(f"🔍 调试信息:")
            print(f"  URL: {url}")
            print(f"  数据: {values}")

            data = {"values": values}
            response = requests.put(url, data=json.dumps(data), headers=self.default_headers, verify=False, timeout=30)

            print(f"  响应状态码: {response.status_code}")
            print(f"  响应内容: {response.text}")

            if response.status_code == 200:
                print(f"✅ 成功写入数据到工作表 {sheet_id}!{cell_range}")
                return True
            else:
                print(f"❌ 写入数据失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print(f"❌ 写入单元格内容失败: {str(e)}")
            return False
    
    def clear_cell_range(self, sheet_name: str, cell_range: str) -> bool:
        """
        清空单元格区域
        
        Args:
            sheet_name: 工作表名称
            cell_range: 单元格范围，如 "A1:C10"
            
        Returns:
            是否成功
        """
        try:
            url = (f"{self.primary_domain}{self.sheet_id}"
                   f"/sheets/{sheet_name}/ranges/{cell_range}/clear"
                   f"?operatorId={self.operator_id}")
            
            response = requests.post(url, headers=self.default_headers, verify=False, timeout=30)
            
            if response.status_code == 200:
                print(f"✅ 成功清空 {sheet_name}!{cell_range}")
                return True
            else:
                print(f"❌ 清空数据失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 清空单元格失败: {str(e)}")
            return False
    

    


    def append_warehouse_report_block(self, sheet_id: str, status_data: Dict, shipping_data: Dict, reward_data: Dict = None, last_row_hint: int = None, start_row_override: int = None) -> tuple:
        """
        追加一个完整的仓库报告块到表格（每次运行添加一个报告块）

        报告块格式：
        - 时间戳
        - 仓库状态数据（待补货、待处理、生产单未完成、已审核）
        - 发货数据
        - 空行分隔

        Args:
            sheet_id: 工作表ID
            status_data: 仓库状态数据
            shipping_data: 发货数据
            last_row_hint: 用于追加模式时，记录的最后行号
            start_row_override: 用于更新模式时，强制指定的开始行号

        Returns:
            (是否成功, 结束行号)
        """
        try:
            # 如果强制指定了开始行，则使用它；否则，自动获取下一个可用行
            if start_row_override:
                next_row = start_row_override
                print(f"🔩 使用强制指定的开始行: {next_row}")
            else:
                # 获取下一个可用的位置，并进行空表检查
                next_row = self._get_next_available_row(sheet_id, last_row_hint)
                if next_row is None:
                    return False, None
                
                # 如果是空表，从第一行开始
                if self.last_row <= 1 and next_row > 2:
                     print(f"⚠️ 检测到可能是空表，强制从第1行开始写入。")
                     next_row = 1

            # 如果不是第一次写入（next_row > 1），处理分隔空行的合并
            # 仅在追加模式下（即非覆盖模式）且不是从第一行开始时，才合并上一行的分隔符
            if not start_row_override and next_row > 1:
                separator_row = next_row - 1  # 分隔空行
                separator_range = f"A{separator_row}:C{separator_row}"
                print(f"🔗 正在合并分隔空行: {separator_range}")
                self._merge_cells(sheet_id, separator_range, "")

            # 准备当前时间 - 使用完整的日期时间对象
            report_datetime_obj = status_data.get('report_date_obj', datetime.now())
            report_datetime_str = report_datetime_obj.strftime('%Y/%m/%d %H:%M:%S')

            # 构建报告数据块
            # A列是标题，B列是值，C列留空或用于第二组数据
            report_data = [
                # 标题行
                ["数据统计时间", report_datetime_str, ""]
            ]
            
            # 从 status_data 提取需要展示的数据
            waiting_stock_orders = status_data.get('待补货', {}).get('订单数', 0)
            waiting_stock_items = status_data.get('待补货', {}).get('品数', 0)
            waiting_process_orders = status_data.get('待处理', {}).get('订单数', 0)
            waiting_process_items = status_data.get('待处理', {}).get('品数', 0)
            unfinished_production_count = status_data.get('生产单未完成单数', {}).get('总数', 0)
            total_production_count = status_data.get('总生产单数', {}).get('总数', 0)
            audited_custom_orders = status_data.get('已审核-定制', {}).get('订单数', 0)
            audited_stock_orders = status_data.get('已审核-现货', {}).get('订单数', 0)

            # 计算生产单完成进度
            if total_production_count > 0:
                finished_count = total_production_count - unfinished_production_count
                progress_text = f"未完成：{unfinished_production_count}/{total_production_count}，已完成：{finished_count}/{total_production_count}"
            else:
                progress_text = "暂无生产单数据"

            # 添加仓库状态数据
            report_data.append(['仓库状态数据', '', ''])
            report_data.append(['类型', '品数', '订单数'])
            report_data.append(['待补货', str(waiting_stock_items), str(waiting_stock_orders)])
            report_data.append(['待处理', str(waiting_process_items), str(waiting_process_orders)])
            report_data.append(['生产单完成进度', '', ''])
            report_data.append(['已审核-定制', '', ''])
            report_data.append(['已审核-现货', '', ''])

            # 添加发货数据
            report_data.append(['发货数据', '', ''])
            report_data.append(['现货订单数', '', ''])
            report_data.append(['现货订单发货总重量', '', ''])
            report_data.append(['定制订单数', '', ''])
            report_data.append(['定制订单发货总重量', '', ''])

            # 添加奖惩数据（如果提供了奖惩数据）
            if reward_data:
                report_data.append(['奖惩计算', '', ''])
                report_data.append(['待补货奖惩', '', ''])
                report_data.append(['待处理奖惩', '', ''])
                report_data.append(['总计奖惩', '', ''])
                report_data.append(['奖惩计算结果', '', ''])  # 新增奖惩结果详情行

            # 计算写入范围
            end_row = next_row + len(report_data) - 1
            cell_range = f"A{next_row}:C{end_row}"

            # 写入报告数据块
            success = self.write_cell_range(sheet_id, cell_range, report_data)

            if success:
                # 先设置所有单元格居中对齐和基本格式
                self._set_comprehensive_format_for_report_block(sheet_id, next_row, end_row)
                # 然后合并单元格并写入数据（包括时间）
                self._merge_cells_for_report_block(sheet_id, next_row, end_row, status_data, shipping_data, report_datetime_str, reward_data)
                # 添加边框
                self._add_borders_to_report_block(sheet_id, next_row, end_row)
                print(f"✅ 成功追加仓库报告块到第 {next_row}-{end_row} 行")
                return True, end_row
            else:
                print(f"❌ 追加仓库报告块失败")
                return False, None

        except Exception as e:
            print(f"❌ 追加仓库数据失败: {str(e)}")
            return False, None

    def write_warehouse_report_data(self, sheet_name: str, status_data: Dict, shipping_data: Dict, pending_orders: List[str] = None) -> bool:
        """
        写入仓库报告数据到钉钉表格（兼容测试接口）

        Args:
            sheet_name: 工作表名称
            status_data: 仓库状态数据
            shipping_data: 发货数据
            pending_orders: 待处理订单列表（可选）

        Returns:
            是否成功
        """
        try:
            # 获取工作表ID
            sheet_id = self._get_sheet_id_by_name(sheet_name)
            if not sheet_id:
                print(f"❌ 找不到工作表: {sheet_name}")
                return False

            # 调用追加报告块方法
            return self.append_warehouse_report_block(sheet_id, status_data, shipping_data)

        except Exception as e:
            print(f"❌ 写入仓库报告数据失败: {str(e)}")
            return False

    def _get_sheet_id_by_name(self, sheet_name: str) -> str:
        """
        根据工作表名称获取工作表ID

        Args:
            sheet_name: 工作表名称

        Returns:
            工作表ID，如果找不到则返回工作表名称本身
        """
        # 对于钉钉表格，通常工作表名称就是ID
        # 如果需要更复杂的映射，可以在这里实现
        return sheet_name

    def _format_report_block(self, sheet_id: str, start_row: int, end_row: int) -> bool:
        """
        设置报告块的格式（居中、字体、加粗等）

        Args:
            sheet_id: 工作表ID
            start_row: 开始行
            end_row: 结束行

        Returns:
            是否成功
        """
        try:
            # 设置整个区域的基本格式：居中、黑体-简 10号字体
            base_format_data = {
                "values": [],  # 不修改值，只修改格式
                "backgroundColors": [],  # 不修改背景色
                "fontFamilies": [["黑体-简"] * 3] * (end_row - start_row + 1),  # 字体
                "fontSizes": [[10] * 3] * (end_row - start_row + 1),  # 字体大小
                "horizontalAlignments": [["center"] * 3] * (end_row - start_row + 1),  # 水平居中
                "verticalAlignments": [["middle"] * 3] * (end_row - start_row + 1),  # 垂直居中
            }

            # 设置整个区域的基本格式
            success1 = self._apply_cell_format(sheet_id, f"A{start_row}:C{end_row}", base_format_data)

            # 设置单个单元格加粗 - 尝试不同的参数名称
            single_bold_format_data = {
                "values": [],  # 不修改值，只修改格式
                "background_colors": [],  # 不修改背景色
                "font_families": [["黑体-简"]],  # 字体
                "font_sizes": [[10]],  # 字体大小
                "font_bolds": [[True]],  # 加粗
                "horizontal_alignments": [["center"]],  # 水平居中
                "vertical_alignments": [["middle"]],  # 垂直居中
            }

            # 设置3列加粗
            three_col_bold_format_data = {
                "values": [],  # 不修改值，只修改格式
                "background_colors": [],  # 不修改背景色
                "font_families": [["黑体-简"] * 3],  # 字体
                "font_sizes": [[10] * 3],  # 字体大小
                "font_bolds": [[True] * 3],  # 加粗
                "horizontal_alignments": [["center"] * 3],  # 水平居中
                "vertical_alignments": [["middle"] * 3],  # 垂直居中
            }

            # 设置所有标题字段加粗
            # 数据生成时间
            self._apply_cell_format(sheet_id, f"A{start_row}:A{start_row}", single_bold_format_data)
            # 仓库状态数据
            self._apply_cell_format(sheet_id, f"A{start_row + 1}:A{start_row + 1}", single_bold_format_data)
            # 类型、品数、订单数
            self._apply_cell_format(sheet_id, f"A{start_row + 2}:C{start_row + 2}", three_col_bold_format_data)
            # 待补货
            self._apply_cell_format(sheet_id, f"A{start_row + 3}:A{start_row + 3}", single_bold_format_data)
            # 待处理
            self._apply_cell_format(sheet_id, f"A{start_row + 4}:A{start_row + 4}", single_bold_format_data)
            # 生产单未完成
            self._apply_cell_format(sheet_id, f"A{start_row + 5}:A{start_row + 5}", single_bold_format_data)
            # 已审核
            self._apply_cell_format(sheet_id, f"A{start_row + 6}:A{start_row + 6}", single_bold_format_data)
            # 发货数据
            self._apply_cell_format(sheet_id, f"A{start_row + 7}:A{start_row + 7}", single_bold_format_data)
            # 发货总金额
            self._apply_cell_format(sheet_id, f"A{start_row + 8}:A{start_row + 8}", single_bold_format_data)

            if success1:
                print(f"✅ 成功设置报告块格式")
                return True
            else:
                print(f"⚠️ 格式设置失败，但数据已成功写入")
                return True  # 即使格式设置失败，也返回True，因为数据写入成功

        except Exception as e:
            print(f"⚠️ 设置格式异常: {str(e)}")
            return True  # 即使格式设置失败，也返回True，因为数据写入成功

    def _apply_cell_format(self, sheet_id: str, cell_range: str, format_data: Dict) -> bool:
        """
        应用单元格格式

        Args:
            sheet_id: 工作表ID
            cell_range: 单元格范围
            format_data: 格式数据

        Returns:
            是否成功
        """
        try:
            # 使用正确的钉钉API端点
            url = f"https://api.dingtalk.com/v1.0/doc/workbooks/{self.sheet_id}/sheets/{sheet_id}/ranges/{cell_range}?operatorId={self.operator_id}"

            # 设置请求头
            headers = {
                'Content-Type': 'application/json',
                'x-acs-dingtalk-access-token': self.access_token
            }

            print(f"🔍 格式设置调试:")
            print(f"  URL: {url}")
            print(f"  格式数据: {format_data}")

            response = requests.put(url, json=format_data, headers=headers)

            # 记录最后一次的响应，方便调试
            self.last_error_response = response.text

            print(f"  响应状态码: {response.status_code}")
            print(f"  响应内容: {response.text}")

            if response.status_code == 200:
                return True
            else:
                print(f"⚠️ 设置格式失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print(f"⚠️ 应用格式异常: {str(e)}")
            return False

    def _merge_cells_for_report_block(self, sheet_id: str, start_row: int, end_row: int, status_data: Dict, shipping_data: Dict, current_time: str, reward_data: Dict = None) -> bool:
        """
        为报告块合并单元格

        Args:
            sheet_id: 工作表ID
            start_row: 开始行
            end_row: 结束行

        Returns:
            是否成功
        """
        try:
            # 按照用户要求合并指定行的B:C列，并设置对应的值
            # 使用传入的时间参数，确保时间格式一致

            # 提取数据
            waiting_stock = status_data.get('待补货', {})
            waiting_process = status_data.get('待处理', {})
            unfinished_production = status_data.get('生产单未完成单数', {})
            total_production = status_data.get('总生产单数', {})
            audited_custom = status_data.get('已审核-定制', {})
            audited_stock = status_data.get('已审核-现货', {})

            # 计算生产单完成进度
            unfinished_count = unfinished_production.get('总数', 0)
            total_count = total_production.get('总数', 0)
            if total_count > 0:
                finished_count = total_count - unfinished_count
                progress_text = f"未完成：{unfinished_count}/{total_count}，已完成：{finished_count}/{total_count}"
            else:
                progress_text = "暂无生产单数据"

            merge_configs = [
                (f"B{start_row}:C{start_row}", current_time),      # 数据生成时间行
                (f"A{start_row + 1}:C{start_row + 1}", "仓库状态数据"),  # 仓库状态数据行（整行合并）
                (f"B{start_row + 5}:C{start_row + 5}", progress_text),  # 生产单完成进度行
                (f"B{start_row + 6}:C{start_row + 6}", str(audited_custom.get('订单数', 0))),  # 已审核-定制行
                (f"B{start_row + 7}:C{start_row + 7}", str(audited_stock.get('订单数', 0))),  # 已审核-现货行
                (f"A{start_row + 8}:C{start_row + 8}", "发货数据"),  # 发货数据标题行（整行合并）
                (f"B{start_row + 9}:C{start_row + 9}", str(shipping_data.get('现货订单数', 0))),  # 现货订单数行
                (f"B{start_row + 10}:C{start_row + 10}", f"{shipping_data.get('现货订单发货总重量', 0.0)}kg"),  # 现货订单发货总重量行
                (f"B{start_row + 11}:C{start_row + 11}", str(shipping_data.get('定制订单数', 0))),  # 定制订单数行
                (f"B{start_row + 12}:C{start_row + 12}", f"{shipping_data.get('定制订单发货总重量', 0.0)}kg"),  # 定制订单发货总重量行
            ]

            # 如果有奖惩数据，添加奖惩相关的合并配置
            if reward_data:
                # 构造奖惩结果详情文本（包含计算规则）
                reward_detail = (f"已审核订单：{reward_data.get('audited_orders', 0):,}单 "
                               f"待补货订单：{reward_data.get('waiting_stock_orders', 0)}单 "
                               f"待处理订单：{reward_data.get('waiting_process_orders', 0)}单 | "
                               f"待补货奖惩（10元规则）：{reward_data.get('stock_condition', '')} "
                               f"结果：{reward_data.get('stock_result', '')} {abs(reward_data.get('stock_reward', 0))}元 | "
                               f"待处理奖惩（25元规则）：{reward_data.get('process_condition', '')} "
                               f"结果：{reward_data.get('process_result', '')} {abs(reward_data.get('process_reward', 0))}元 | "
                               f"总计奖惩：{reward_data.get('total_reward', 0):+d}元")

                reward_configs = [
                    (f"A{start_row + 13}:C{start_row + 13}", "奖惩计算"),  # 奖惩计算标题行（整行合并）
                    (f"B{start_row + 14}:C{start_row + 14}", f"{reward_data.get('stock_result', '')} {reward_data.get('stock_reward', 0):+d}元"),  # 待补货奖惩行
                    (f"B{start_row + 15}:C{start_row + 15}", f"{reward_data.get('process_result', '')} {reward_data.get('process_reward', 0):+d}元"),  # 待处理奖惩行
                    (f"B{start_row + 16}:C{start_row + 16}", f"{reward_data.get('total_reward', 0):+d}元"),  # 总计奖惩行
                    (f"A{start_row + 17}:C{start_row + 17}", reward_detail),  # 奖惩计算结果详情行（整行合并）
                ]
                merge_configs.extend(reward_configs)

            for merge_range, value in merge_configs:
                success = self._merge_cells(sheet_id, merge_range, value)
                if not success:
                    print(f"⚠️ 合并单元格失败: {merge_range}")

            # 注意：钉钉在线表格API不支持行高设置功能
            # 如果有奖惩数据，原本计划为奖惩计算结果行设置35像素行高，但API不支持此功能

            return True

        except Exception as e:
            print(f"⚠️ 合并单元格异常: {str(e)}")
            return False



    def find_last_data_row(self, sheet_id: str, max_check_rows: int = 1000) -> int:
        """
        高效地查找表格中最后一行有数据的行号

        Args:
            sheet_id: 工作表ID
            max_check_rows: 一次性读取的最大行数

        Returns:
            最后一行有数据的行号，如果表格为空返回0
        """
        try:
            print(f"🔍 正在高效查找最后一行数据（一次性读取最多 {max_check_rows} 行）...")
            
            # 一次性读取一个大范围的数据
            scan_range = f"A1:C{max_check_rows}"
            all_data = self.get_cell_range(sheet_id, scan_range)

            if not all_data:
                print("📍 表格为空，未找到任何数据。")
                return 0

            # 从后往前遍历获取到的数据，找到第一个非空行
            for i in range(len(all_data) - 1, -1, -1):
                row_data = all_data[i]
                # 检查行内是否有任何非空单元格
                if any(cell and str(cell).strip() for cell in row_data):
                    # 行号是索引 + 1
                    last_row = i + 1
                    print(f"📍 找到最后数据行: {last_row}")
                    return last_row
            
            # 如果遍历完所有行都没有数据
            print("📍 读取到了数据范围，但所有行都为空。")
            return 0

        except Exception as e:
            print(f"❌ 查找最后数据行失败: {str(e)}")
            return 0

    def _check_and_correct_last_row(self, sheet_id: str, recorded_last_row: int) -> int:
        """
        校正记录的最后行号。
        通过扫描记录行号上下200行的范围来找到实际的最后数据行。
        这可以处理手动删除或添加行导致记录不准的情况。

        Args:
            sheet_id: 工作表ID
            recorded_last_row: 配置文件中记录的最后一行

        Returns:
            校正后的最后一行号
        """
        try:
            # 如果记录的行号过小，直接进行全局扫描更安全
            if recorded_last_row < 20:
                print(f"ℹ️ 记录行号({recorded_last_row})过小，直接执行全局扫描以确保准确性。")
                return self.find_last_data_row(sheet_id)
            
            # 智能探测：先检查记录行附近是否为空，如果为空，则很可能表格被清空，应立即进行全局扫描
            probe_range = f"A{max(1, recorded_last_row - 5)}:C{recorded_last_row + 20}"
            print(f"🔬 智能探测 {probe_range} 范围内是否有数据...")
            probe_data = self.get_cell_range(sheet_id, probe_range)
            if not any(any(cell and str(cell).strip() for cell in row) for row in probe_data):
                print(f"⚠️ 智能探测发现记录行附近为空，触发全局扫描...")
                return self.find_last_data_row(sheet_id)
            print("✅ 智能探测通过，记录行附近有数据，开始局部扫描。")


            # 定义扫描范围
            scan_radius = 200
            start_scan = max(1, recorded_last_row - scan_radius)
            end_scan = recorded_last_row + scan_radius

            print(f"🔍 开始在 {start_scan}-{end_scan} 行范围内进行局部扫描以校正行号...")

            actual_last_row = 0
            # 从扫描范围的末尾向前搜索，以最高效率找到最后一行数据
            for row in range(end_scan, start_scan - 1, -1):
                try:
                    cell_data = self.get_cell_range(sheet_id, f"A{row}:C{row}")
                    # 检查行内是否有任何非空单元格
                    if cell_data and cell_data[0] and any(cell and str(cell).strip() for cell in cell_data[0]):
                        actual_last_row = row
                        # 找到第一个有数据行后就跳出
                        break
                except Exception:
                    # 忽略单行检查的失败
                    continue
            
            # 如果局部扫描没有找到任何数据（可能是一个大范围被删除了）
            if actual_last_row == 0 and start_scan > 1:
                print(f"⚠️ 局部扫描在 {start_scan}-{end_scan} 范围内未找到任何数据，将触发全局扫描...")
                return self.find_last_data_row(sheet_id)

            if actual_last_row != recorded_last_row:
                print(f"📝 行号已校正: 配置文件记录为 {recorded_last_row}, 实际为 {actual_last_row}")
            else:
                print(f"✅ 行号记录准确，无需校正: {recorded_last_row}")

            return actual_last_row

        except Exception as e:
            print(f"⚠️ 行号校正过程中发生异常: {str(e)}。将退回使用全局扫描。")
            return self.find_last_data_row(sheet_id)

    def _merge_cells(self, sheet_id: str, cell_range: str, value: str = "") -> bool:
        """
        合并指定范围的单元格并设置值和格式

        Args:
            sheet_id: 工作表ID
            cell_range: 要合并的单元格范围，如 "B1:C1"
            value: 要在合并后的单元格中显示的值

        Returns:
            是否成功
        """
        try:
            # 先合并单元格
            merge_url = f"https://api.dingtalk.com/v1.0/doc/workbooks/{self.sheet_id}/sheets/{sheet_id}/ranges/{cell_range}/merge?operatorId={self.operator_id}"

            headers = {
                'Content-Type': 'application/json',
                'x-acs-dingtalk-access-token': self.access_token
            }

            # 使用POST方法合并
            merge_response = requests.post(merge_url, headers=headers, verify=False)

            if merge_response.status_code == 200:
                print(f"✅ 成功合并单元格: {cell_range}")

                # 如果有值，则设置合并后单元格的值和格式
                if value:
                    # 获取合并后的起始单元格（如B1:C1合并后是B1）
                    start_cell = cell_range.split(':')[0]

                    # 设置值和居中格式 - 使用正确的钉钉API参数名称，并添加加粗
                    format_data = {
                        "values": [[value]],
                        "horizontalAlignments": [["center"]],
                        "verticalAlignments": [["middle"]],
                        "fontFamilies": [["SimHei"]],  # 黑体
                        "fontSizes": [[10]],           # 10号字体
                        "fontBolds": [[True]]          # 加粗字体
                    }

                    # 如果是时间格式，强制设置为文本格式以避免转换为数字
                    if "/" in value and ":" in value:  # 检测时间格式
                        # 使用文本格式而不是日期格式，避免Excel自动转换
                        format_data["numberFormats"] = [["@"]]  # @ 表示文本格式
                        print(f"🕐 检测到时间格式，设置为文本格式: {value}")

                    format_url = f"https://api.dingtalk.com/v1.0/doc/workbooks/{self.sheet_id}/sheets/{sheet_id}/ranges/{start_cell}?operatorId={self.operator_id}"
                    format_response = requests.put(format_url, json=format_data, headers=headers, verify=False)

                    if format_response.status_code == 200:
                        print(f"✅ 成功设置合并单元格的值和格式: {start_cell} = '{value}'")
                    else:
                        print(f"⚠️ 设置合并单元格格式失败: {format_response.status_code} - {format_response.text}")

                return True
            else:
                print(f"⚠️ 合并单元格失败: {merge_response.status_code} - {merge_response.text}")
                return False

        except Exception as e:
            print(f"⚠️ 合并单元格异常: {str(e)}")
            return False

    def _add_borders_to_report_block(self, sheet_id: str, start_row: int, end_row: int) -> bool:
        """
        为报告块添加边框
        注意：钉钉表格API不支持边框设置，此方法仅作为占位符

        Args:
            sheet_id: 工作表ID
            start_row: 开始行
            end_row: 结束行

        Returns:
            是否成功（总是返回True，因为边框功能不可用）
        """
        try:
            range_notation = f"A{start_row}:C{end_row}"
            print(f"ℹ️ 钉钉表格API不支持边框设置，跳过边框设置: {range_notation}")
            return True

        except Exception as e:
            print(f"⚠️ 边框方法异常: {str(e)}")
            return True  # 即使异常也返回True，因为边框不是必需功能



    def _set_comprehensive_format_for_report_block(self, sheet_id: str, start_row: int, end_row: int) -> bool:
        """
        为报告块设置综合格式（居中、字体、字号等）

        Args:
            sheet_id: 工作表ID
            start_row: 开始行
            end_row: 结束行

        Returns:
            是否成功
        """
        try:
            # 设置整个报告块的综合格式
            range_notation = f"A{start_row}:C{end_row}"

            # 计算行数和列数
            rows = end_row - start_row + 1
            cols = 3

            # 创建格式数组 - 使用正确的钉钉API参数名称，并添加加粗功能
            format_data = {
                "horizontalAlignments": [["center"] * cols for _ in range(rows)],  # 水平居中
                "verticalAlignments": [["middle"] * cols for _ in range(rows)],    # 垂直居中
                "fontFamilies": [["SimHei"] * cols for _ in range(rows)],          # 黑体
                "fontSizes": [[10] * cols for _ in range(rows)],                   # 10号字体
                "fontBolds": [[True] * cols for _ in range(rows)]                  # 加粗字体
            }

            url = f"https://api.dingtalk.com/v1.0/doc/workbooks/{self.sheet_id}/sheets/{sheet_id}/ranges/{range_notation}?operatorId={self.operator_id}"

            headers = {
                'Content-Type': 'application/json',
                'x-acs-dingtalk-access-token': self.access_token
            }

            response = requests.put(url, json=format_data, headers=headers, verify=False)

            if response.status_code == 200:
                print(f"✅ 成功设置综合格式: {range_notation}")
                return True
            else:
                print(f"⚠️ 设置综合格式失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print(f"⚠️ 设置综合格式异常: {str(e)}")
            return False

    def find_report_block_by_date(self, sheet_id: str, target_date: str) -> int:
        """
        根据日期查找报告块的起始行号。

        Args:
            sheet_id: 工作表ID
            target_date: 目标日期字符串，格式为 "YYYY/MM/DD"

        Returns:
            如果找到，返回报告块的起始行号；否则返回 None
        """
        print(f"🔍 正在表格中搜索日期为 '{target_date}' 的记录...")
        try:
            # 优化：从记录的最后一行向上扫描500行，提高效率
            scan_start_row = max(1, self.last_row - 500)
            # 确保扫描范围的起始不大于结束
            scan_end_row = self.last_row
            if scan_start_row > scan_end_row:
                scan_start_row = 1
            
            scan_range = f"B{scan_start_row}:B{scan_end_row}"
            print(f"   - 优化扫描范围: {scan_range}")

            # 修复：日期时间戳在B列，所以应该读取B列
            range_data = self.get_cell_range(sheet_id, scan_range)
            if not range_data:
                return None

            for i, row in enumerate(range_data):
                if row and row[0]: # get_cell_range返回的是二维数组，即使只读一列，内层也还有一个数组
                    cell_value = str(row[0]).strip()
                    # 检查单元格是否包含目标日期
                    if target_date in cell_value:
                        # 行号是 扫描起始行 + 相对索引
                        row_number = scan_start_row + i
                        print(f"✅ 在第 {row_number} 行找到了匹配的日期记录。")
                        return row_number
            
            print(f"ℹ️ 未找到日期为 '{target_date}' 的记录。")
            return None

        except Exception as e:
            print(f"❌ 按日期查找记录时出错: {str(e)}")
            return None

    def get_all_recorded_dates(self, sheet_id: str) -> List[str]:
        """
        获取指定工作表中所有已记录的日期。

        Args:
            sheet_id: 工作表ID

        Returns:
            一个包含所有日期的字符串列表，格式为 "YYYY/MM/DD"
        """
        print("🔍 正在扫描已记录的所有日期...")
        recorded_dates = []
        try:
            # 优化：从记录的最后一行向上扫描500行，提高效率
            scan_start_row = max(1, self.last_row - 500)
            # 确保扫描范围的起始不大于结束
            scan_end_row = self.last_row
            if scan_start_row > scan_end_row:
                scan_start_row = 1

            scan_range = f"B{scan_start_row}:B{scan_end_row}"
            print(f"   - 优化扫描范围: {scan_range}")

            # 修复：日期时间戳在B列，所以应该读取B列
            range_data = self.get_cell_range(sheet_id, scan_range)
            if not range_data:
                return []

            for row in range_data:
                if row and row[0]:
                    cell_value = str(row[0]).strip()
                    # 使用正则表达式从 "YYYY/MM/DD HH:MM:SS" 中提取日期部分
                    match = re.search(r"(\d{4}/\d{2}/\d{2})", cell_value)
                    if match:
                        recorded_dates.append(match.group(1))
            
            # 去重并排序
            unique_dates = sorted(list(set(recorded_dates)))
            print(f"✅ 扫描完成，找到 {len(unique_dates)} 个唯一日期。")
            return unique_dates

        except Exception as e:
            print(f"❌ 扫描已记录日期时出错: {str(e)}")
            return []

    def _get_next_available_row(self, sheet_id: str, last_row_hint: int = None) -> int:
        """
        获取下一个可用的行号（用于追加报告块）
        会自动在新数据前留出一个空行作为分隔

        Args:
            sheet_id: 工作表ID
            last_row_hint: 上次记录的最后一行号（可选，用于优化性能）

        Returns:
            下一行的行号，失败返回None
        """
        try:
            # 如果有上次记录的最后一行提示，直接使用（已在写入前完成检测和校正）
            if last_row_hint and last_row_hint > 0:
                print(f"📍 使用配置文件中记录的最后一行: {last_row_hint}")
                last_data_row = last_row_hint
            else:
                # 修复：调用健壮的 `find_last_data_row` 方法来找到真正的最后一行
                print("🔍 配置文件无记录，开始全表扫描最后一行...")
                last_data_row = self.find_last_data_row(sheet_id)

            # 如果是第一次写入数据（没有找到任何数据）
            if last_data_row == 0:
                print("📍 表格为空，将从第1行开始写入")
                return 1

            # 如果已有数据，在最后一行数据后留出一个空行，然后返回下一行
            next_row = last_data_row + 2  # +1是紧接着的行，+2是留出一个空行

            print(f"📍 找到最后数据行: {last_data_row}，下一个数据块将从第 {next_row} 行开始（留出1行空行分隔）")
            return next_row

        except Exception as e:
            print(f"❌ 获取下一行位置失败: {str(e)}")
            # 如果完全失败，返回一个默认位置
            print("⚠️ 使用默认位置第1行")
            return 1
